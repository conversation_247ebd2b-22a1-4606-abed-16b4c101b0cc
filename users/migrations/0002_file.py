# Generated by Django 4.0.2 on 2022-05-12 10:23

from django.db import migrations, models
import stocktick.storage_backends


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='File',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('file', models.FileField(blank=True, null=True, storage=stocktick.storage_backends.MediaStorage, upload_to='')),
            ],
            options={
                'db_table': 'tbl_file',
                'managed': False,
            },
        ),
    ]
