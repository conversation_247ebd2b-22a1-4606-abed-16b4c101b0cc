# Generated by Django 4.0.2 on 2022-05-12 09:29

from django.db import migrations, models
import stocktick.storage_backends


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TblRoles',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_date', models.DateTimeField(blank=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_date', models.CharField(blank=True, max_length=45, null=True)),
                ('active', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('rolename', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
            ],
            options={
                'db_table': 'tbl_roles',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblUserDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('age', models.IntegerField(blank=True, null=True)),
                ('email', models.CharField(blank=True, max_length=75, null=True)),
                ('email_verified', models.TextField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, max_length=45, null=True)),
                ('name', models.CharField(blank=True, max_length=85, null=True)),
                ('profile_url', models.TextField(blank=True, max_length=445, null=True)),
                ('user_id', models.IntegerField(unique=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_user_details',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblUsers',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_date', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_date', models.DateTimeField(blank=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('active', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('phone', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('otp', models.IntegerField(blank=True, null=True)),
                ('otp_expiry', models.DateTimeField(blank=True, null=True)),
                ('fundlogin', models.BooleanField(default=0)),
                ('profile_img', models.ImageField(blank=True, storage=stocktick.storage_backends.MediaStorage, upload_to='')),
            ],
            options={
                'db_table': 'tbl_users',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblUserSessions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('active', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('token_last_used', models.DateTimeField(blank=True, null=True)),
                ('token', models.CharField(blank=True, max_length=145, null=True)),
                ('token_expiry', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tbl_user_sessions',
                'managed': False,
            },
        ),
    ]
