from django.shortcuts import render
import json
import regex as re
from random import randint, randrange

# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings

from uuid import uuid4
import pytz
import datetime

from .models import TblUsers, TblUserDetails
from .serializer import TblUsersSerializer, TblUserDetailsSerializer, FileSerializer
from users import serializer
from .textlocal import sendOTP


def getCurrentTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    return str(datetime.datetime.now(IST))[:-6]


# Create your views here.


def getOTPExpiryTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    now = datetime.datetime.now(IST) + datetime.timedelta(minutes=5)
    print(now)
    return str(now)[:-6]


# Create your views here.


def getTokenExpiryTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    now = datetime.datetime.now(IST) + datetime.timedelta(days=30)
    print(now)
    return str(now)[:-6]


def isValid(s):
    # 1) Begins with 0 or 91
    # 2) Then contains 7 or 8 or 9.
    # 3) Then contains 9 digits
    Pattern = re.compile("(0|91)?[6-9][0-9]{9}")
    return Pattern.match(s)



def verifyUser(token):
    conn = connection
    cur = conn.cursor()

    now = getCurrentTimestamp()
    token_expiry = getTokenExpiryTimestamp()
    q = "SELECT user_id,token_expiry FROM tbl_user_sessions WHERE token = '%s'" % token
    cur.execute(q)
    res = cur.fetchone()
    print(q)

    if res:
        # USER_ID = res['id']
        if str(res[1]) < now:
            cur.close()
            # conn.close()
            return {"message": False, "reason": "Token Expired"}

        USER_ID = res[0]
        update = "UPDATE tbl_user_sessions SET token_last_used = '%s', token_expiry = '%s' WHERE user_id = %s " % (
            now, token_expiry, res[0])
        cur.execute(update)
        conn.commit()
        print(update)
        cur.close()
        # conn.close()

        return {"message": True, "id": res[0], "reason": "Success"}
    else:
        cur.close()
        # conn.close()
        return {"message": False, "reason": "Token Not Found"}


@api_view(["GET"])
def getAllOTP(req):
    try:
        cur = connection.cursor()
        q = "SELECT phone,otp FROM tbl_users"
        cur.execute(q)
        print(q)
        res = cur.fetchall()
        lis = []

        for r in res:
            lis.append({r[0]: r[1]})
        return JsonResponse(lis, safe=False)
    except Exception as e:
        print(e.args[0])
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def GetOtp(req):
    try:
        body = json.loads(req.body)
        username = body["username"]
        print(body)
        if isValid(username):
            print("sahi hai")
            # if username=="8761913204":
            #     return JsonResponse({"message": "successfully sent OTP"})
            # search number in db
            now = getCurrentTimestamp()
            otp_expiry = getOTPExpiryTimestamp()
            otp = randrange(100000, 1000000)
            otp="000000"
            # if username=="8761913204":
            #     otp="111111"
            conn = connection
            cur = conn.cursor()
            q = "SELECT id,phone FROM tbl_users WHERE phone = '%s'" % username
            cur.execute(q)
            res = cur.fetchone()
            print(res)

            if res:
                # Update query
                qq = "UPDATE tbl_users SET otp = %s, otp_expiry = '%s' WHERE id = %s" % (
                    otp, otp_expiry, res[0])
                cur.execute(qq)
                conn.commit()
            else:
                qq = "INSERT INTO tbl_users (created_date,created_by,phone,otp,otp_expiry,role) VALUES ('%s','System Generated','%s',%s,'%s',4)" % (
                    now, username, otp, otp_expiry)
                cur.execute(qq)
                conn.commit()
            # if username!="8761913204" and  not sendOTP(username, otp):   #set the test parameter to False after finalizing
            #     return Response({"message": "Error in sending OTP"}, status=status.HTTP_400_BAD_REQUEST)
            print(qq)
            cur.close()
            # conn.close()
            return JsonResponse({"message": "successfully sent OTP"})
            # if found send otp otherwise add to db and send otp

        else:
            dict1 = {"message": "Wrong Number Format"}
            return Response(dict1, status=status.HTTP_400_BAD_REQUEST)
        return JsonResponse({"username": username}, safe=False)
    except Exception as e:
        print(e.args[0])
        dict1 = {"message": "Input fields not correct"}
        return Response(dict1, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def validateOtp(req):
    try:
        body = json.loads(req.body)
        try:
            username = body["username"]
            otp = body["otp"]
        except Exception as e:
            return Response({"message": "Input Fields are missing or Wrong"}, status=status.HTTP_400_BAD_REQUEST)

        conn = connection
        cur = conn.cursor()

        search_q = "SELECT id,(SELECT name FROM tbl_user_details WHERE user_id = (SELECT id FROM tbl_users WHERE phone = '%s'))as full_name,phone,otp,otp_expiry FROM tbl_users WHERE phone = '%s'" % (
            username, username)
        cur.execute(search_q)
        res = cur.fetchone()

        if not isinstance(otp, int):
            otp = int(otp)

        if not res:
            # conn.close()
            cur.close()
            return Response({"message": "No Entries Found for this number"}, status=status.HTTP_404_NOT_FOUND)
        print(res[3])
        if otp == res[3]:
            print('correct otp')
            now = getCurrentTimestamp()
            token = uuid4()
            token_expiry = getTokenExpiryTimestamp()
            print(token)
            check_q = "SELECT id FROM tbl_user_sessions WHERE user_id = %s" % res[0]
            cur.execute(check_q)
            users_res = cur.fetchone()

            # check for OTP expiry
            if users_res:
                update_q = "UPDATE `stocktick-qa`.tbl_user_sessions SET token = '%s', modified_at = '%s',token_expiry='%s',token_last_used='%s' WHERE user_id = %s" % (
                    token, now, token_expiry, now, res[0])
            else:
                update_q = "INSERT `stocktick-qa`.tbl_user_sessions (created_by,created_at,token,token_expiry,token_last_used,user_id) VALUES ('System-generated','%s','%s','%s','%s','%s')" % (
                    now, token, token_expiry, now, res[0]
                )
            cur.execute(update_q)
            print(update_q)
            conn.commit()

            cur.close()
            # conn.close()

            if res[1] is not None:
                old_user = True
            else:
                old_user = False

            return JsonResponse({"message": "Logged In Successfully", "authToken": token, "Old_User": old_user})
        else:
            return Response({"message": "Wrong OTP!"}, status=status.HTTP_401_UNAUTHORIZED)

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["PUT"])
def updateUserInfo(req):
    try:
        body = json.loads(req.body)
        # print(req.headers)
        try:
            token = req.headers["authToken"]
            response = verifyUser(token)
            print(token)
            if response["message"]:
                user_id = response['id']
                print(user_id, 'hola')
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            try:
                token = req.query_params['authToken']
                response = verifyUser(token)
                print(token)
                if response["message"]:
                    user_id = response['id']
                    print(user_id, 'hola')
                else:
                    return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

            except Exception as e:
                print(e.args)
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        name = 'null'
        age = 'null'
        gender = 'null'
        email = 'null'
        profile_url = 'null'
        try:

            if body["name"] == "NA":
                name = 'null'
            else:
                name = "'" + body["name"] + "'"
        except:
            pass

        try:
            if body['age'] == -1:
                age = 'null'
            else:
                age = body['age']
        except:
            pass

        try:
            if body["gender"] == "NA":
                gender = 'null'
            else:
                gender = "'" + body["gender"] + "'"
        except:
            pass

        try:
            if body["email"] == "NA":
                email = 'null'
            else:
                email = "'" + body["email"] + "'"
        except:
            pass

        try:
            if body['profile_url'] == "NA":
                profile_url = 'null'
            else:
                profile_url = "'" + body['profile_url'] + "'"
        except:
            pass

        now = getCurrentTimestamp()

        conn = connection
        cur = conn.cursor()

        qq = "SELECT name FROM tbl_user_details  WHERE user_id = %s" % user_id
        cur.execute(qq)
        res = cur.fetchone()

        if res:
            update_q = "UPDATE  tbl_user_details SET name = %s,age = %s, gender=%s,email= %s,profile_url = %s,modified_at = '%s'  WHERE user_id = %s" % (
                name, age, gender, email, profile_url, now, user_id
            )
        else:
            update_q = "INSERT INTO tbl_user_details (created_by,created_at,name,age,gender,email,profile_url,user_id) VALUES ('System-generated','%s',%s, %s ,%s,%s,%s,%s)" % (
                now, name, age, gender, email, profile_url, user_id
            )
        print(update_q)
        cur.execute(update_q)
        conn.commit()

        cur.close()
        # conn.close()

        return JsonResponse({"message": "Successfully Updated Details"})

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
def getUserInfo(req):
    try:
        try:
            token = req.headers["authToken"]
            # print(token)
            response = verifyUser(token)
            if response["message"]:
                user_id = response['id']
                print(user_id, 'hola')
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            try:
                token = req.query_params['authToken']
                response = verifyUser(token)
                if response["message"] is True:
                    user_id = response['id']
                    print(user_id, 'hola')
                else:
                    return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

            except Exception as e:
                print(e.args)
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        get_q = "SELECT name,gender,age,(SELECT phone FROM tbl_users WHERE id = %s) as phone,email,profile_url FROM tbl_user_details WHERE user_id = %s" % (
            user_id, user_id)
        print(get_q)

        cur.execute(get_q)
        res = cur.fetchone()

        print(res)
        dct = {}
        if not res:
            get_q = "SELECT phone FROM `stocktick-qa`.tbl_users WHERE id = %s" % user_id
            cur.execute(get_q)

            res = cur.fetchone()
            dct["name"] = None
            dct["gender"] = None
            dct["age"] = None
            dct["phone"] = res[0]
            dct["email"] = None
            dct["profile_url"] = None
        else:
            dct["name"] = res[0]
            dct["gender"] = res[1]
            dct["age"] = res[2]
            dct["phone"] = res[3]
            dct["email"] = res[4]
            dct["profile_url"] = res[5]
        cur.close()
        # conn.close()

        return JsonResponse(dct, safe=False)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateUserAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            users = TblUserDetails.objects.filter(user_id=userId)
            users = TblUserDetailsSerializer(users, many=True)
            return Response(users.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        name = data["name"]
        email = data["email"]
        age = data["age"]
        gender = data["gender"]
        profile_url = data["profile_url"]
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            exists = TblUserDetails.objects.get(user_id=userId)
        except:
            exists = -1
        print(exists)
        if (exists != -1):

            exists.modified_by = userId
            exists.name = name
            exists.email = email
            exists.age = age
            exists.gender = gender
            exists.profile_url = profile_url
            exists.active = True
            exists.save()
            return Response({"message": "User details Updated"}, status=status.HTTP_201_CREATED)

        else:
            if data:
                data["created_by"] = userId
                data["modified_by"] = userId
                data["user_id"] = userId
                data["profile_url"] = profile_url

            serializer = TblUserDetailsSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class FileUploader(APIView):
    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        # print(request.FILES, request.data, request.POST)
        serializer = FileSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response({"message": "Success"}, status=status.HTTP_201_CREATED)
