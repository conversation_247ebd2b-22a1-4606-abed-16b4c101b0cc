from django.db import models
from users.constant import ROW_STATUS, ACTIVE
from stocktick.storage_backends import MediaStorage
# Create your models here.


class TblRoles(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_date = models.DateTimeField(blank=True, null=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_date = models.CharField(max_length=45, blank=True, null=True)
    active = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    rolename = models.Char<PERSON>ield(max_length=45, blank=True, null=True)

    def __str__(self):
        return self.rolename

    class Meta:
        managed = False
        db_table = 'tbl_roles'


class TblUsers(models.Model):
    created_date = models.DateTimeField(blank=True, null=True)
    created_by = models.Char<PERSON>ield(max_length=45, blank=True, null=True)
    modified_date = models.DateTimeField(blank=True, null=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    active =models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    phone = models.CharField(unique=True, max_length=10, blank=True, null=True)
    role = models.ForeignKey(TblRoles, models.DO_NOTHING,
                             db_column='role', blank=True, null=True)
    otp = models.IntegerField(blank=True, null=True)
    otp_expiry = models.DateTimeField(blank=True, null=True)
    fundlogin = models.BooleanField(default=0)          #fund Bazaar login
    profile_img = models.ImageField(storage = MediaStorage, blank=True)

    def __str__(self):
        return self.phone

    class Meta:
        managed = False
        db_table = 'tbl_users'

class TblContactUs(models.Model):
    created_date = models.DateTimeField(blank=True, null=True)
    name = models.CharField(max_length=85, blank=True, null=True)
    phone = models.CharField(unique=True, max_length=10, blank=True, null=True)
    email = models.CharField(max_length=75, blank=True, null=True)
    query = models.CharField(max_length=1000, blank=True, null=True)

    def __str__(self):
        return self.phone

    class Meta:
        managed = False
        db_table = 'tbl_contact_us'


class TblUserDetails(models.Model):
    created_at = models.DateTimeField(blank=True, null=True,auto_now=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True,auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    age = models.IntegerField(blank=True, null=True)
    email = models.CharField(max_length=75, blank=True, null=True)
    # This field type is a guess.
    email_verified = models.TextField(blank=True, null=True)
    gender = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=85, blank=True, null=True)
    profile_url = models.TextField(max_length=445, blank=True, null=True)
    user_id = models.IntegerField(blank=False, unique=True, null=False)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)

    def __str__(self):
        return self.name

    class Meta:
        managed = False
        db_table = 'tbl_user_details'


class TblUserSessions(models.Model):
    created_at = models.DateTimeField(blank=True, null=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    active = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    token_last_used = models.DateTimeField(blank=True, null=True)
    token = models.CharField(max_length=145, blank=True, null=True)
    token_expiry = models.DateTimeField(blank=True, null=True)
    user = models.ForeignKey(
        'TblUsers', models.DO_NOTHING, blank=True, null=True)

    def __str__(self):
        return self.user.phone

    class Meta:
        managed = False
        db_table = 'tbl_user_sessions'


class File(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    file = models.FileField(storage = MediaStorage, blank=True, null=True)

    def __str__(self):
        return self.file

    class Meta:
        managed = False
        db_table = 'tbl_file'