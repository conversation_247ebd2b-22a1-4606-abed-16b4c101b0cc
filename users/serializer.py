from rest_framework import serializers
from .models import TblUsers, TblUserDetails,File

class TblUsersSerializer(serializers.ModelSerializer):
    class Meta:
        model = TblUsers
        fields = '__all__'


class TblUserDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TblUserDetails
        fields = '__all__'

class FileSerializer(serializers.ModelSerializer):
    class Meta:
        model = File
        fields = '__all__'

    def create(self, validated_data):
        file_obj = self.Meta.model(**validated_data)
        file_obj.save()
        return file_obj
