import urllib.request
import urllib.parse
import json

import os
from dotenv import load_dotenv

load_dotenv()

textLocalAPIKey = os.getenv("TEXTLOCAL_KEY")
smsMode = os.getenv("SMS_TEST_MODE")


def sendOTP(phone, otp):
    # return 1
    def sendSMS(apikey, numbers, sender, message):
        data = urllib.parse.urlencode({'apikey': apikey, 'numbers': numbers,
                                       'message': message, 'sender': sender, 'test': smsMode})
        data = data.encode('utf-8')
        request = urllib.request.Request("https://api.textlocal.in/send/?")
        f = urllib.request.urlopen(request, data)
        fr = f.read()
        return (fr)

    msg = """Dear User,

Your OTP for login to Stocktick Capital ( A Venture of Waymark Enterprises Pvt Ltd) portal is {}. 

Regards,
Stocktick Capital -Team""".format(otp)
    print(msg)
    resp = sendSMS(textLocalAPIKey,
                   phone, 'STKCAP', msg)
    res_dict = json.loads(resp.decode('utf-8'))
    print("\n", res_dict)
    return 0 if res_dict['status'] == 'failure' else 1

# if not sendOTP('8828306812',123456):
#     print("fail")
# else:
#     print("success")
