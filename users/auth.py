from time import strptime
from .models import TblUserSessions

import pytz
import datetime

def getTokenExpiryTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    now = datetime.datetime.now(IST) + datetime.timedelta(days=30)
    return str(now)[:-6]


def getCurrentTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    return str(datetime.datetime.now(IST))[:-6]

def verifyUser(token):
    try:
        user = TblUserSessions.objects.get(token=token)

        if not user:
            return {"message": False, "reason": "Token Not Found"}
        now = getCurrentTimestamp()
        token_expiry = getTokenExpiryTimestamp()

        
        if str(user.token_expiry)<now:
            return {"message": False, "reason": "Token Expired"}

        user.token_last_used=now
        user.token_expiry=token_expiry
        user.save()

        return {"message": True, "id": user.user.id, "reason": "Success"}
    except Exception as e:
        return {"message": False, "reason": "Internal Server Error"}

