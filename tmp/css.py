CSS = """
<style>
        .container{
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px;
        }

        .header{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
            padding: 2px;
            margin-bottom: 35px;
        }

        .title{
            font-size: 25px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            background-color: rgb(99, 124, 40);
            padding: 5px;
            width: 350px;
            text-align: center;
            border-radius: 5px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            transform: translateX(50px);
            margin: 10px;
        }

        .date{
            font-size: 15px;
        }

        .content{
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .qn{
            padding: 8px;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-color: rgb(221, 222, 223);
            color: rgb(56, 56, 56);
            border-radius: 3px ;
        }

        .ans{
            padding: 8px;
            font-size: 18px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-color: rgb(239, 239, 239);
            color: rgb(56, 56, 56);
            border-radius: 3px ;
            margin-top: 5px;
            margin-bottom: 5px;
        }
        #bt{
            margin: 20px;
        }

        .footer{
            background-color: rgb(221, 222, 223);
            width: 100%;
            padding: 10px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            text-align: center;
            font-size: 20px;
        }

        .bottom{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            /* height: 150px; */
        }

        .report{
            width: 42%;
            margin: 5px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            font-weight: bold;
        }
        .report h3{
            margin-top: -18px;
        }


        #img1{
            width: 80%;
            position: relative;
        }
        #img2{
            width: 15%;
            position: absolute;
            transition: translatex(3px);
            left: 22%;
            bottom: 42%;
        }


        .metre{
            width: 45%;
        }

        #txt{
            font-size: 26px;
            position: absolute;
            top: 55%;
            left: 74px;
        }
    </style>
"""