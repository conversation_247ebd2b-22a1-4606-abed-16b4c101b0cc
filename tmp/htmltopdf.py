import pdfkit
name = "Rupak"
k = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report</title>
    <style>
        .container{
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px;
        }

        .header{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
            padding: 2px;
            margin-bottom: 35px;
        }

        .title{
            font-size: 25px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            background-color: rgb(99, 124, 40);
            padding: 5px;
            width: 350px;
            text-align: center;
            border-radius: 5px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            transform: translateX(50px);
            margin: 10px;
        }

        .date{
            font-size: 15px;
        }

        .content{
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .qn{
            padding: 8px;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-color: rgb(221, 222, 223);
            color: rgb(56, 56, 56);
            border-radius: 3px ;
        }

        .ans{
            padding: 8px;
            font-size: 18px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-color: rgb(239, 239, 239);
            color: rgb(56, 56, 56);
            border-radius: 3px ;
            margin-top: 5px;
            margin-bottom: 5px;
        }
        #bt{
            margin: 20px;
        }

        .footer{
            background-color: rgb(221, 222, 223);
            width: 100%;
            padding: 10px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            text-align: center;
            font-size: 20px;
        }

        .bottom{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            /* height: 150px; */
        }

        .report{
            width: 42%;
            margin: 5px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            font-weight: bold;
        }
        .report h3{
            margin-top: -18px;
        }


        #img1{
            width: 80%;
            position: relative;
        }
        #img2{
            width: 15%;
            position: absolute;
            transition: translatex(3px);
            left: 22%;
            bottom: 42%;
        }


        .metre{
            width: 45%;
        }

        #txt{
            font-size: 26px;
            position: absolute;
            top: 55%;
            left: 74px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="logo2.png" alt="" srcset="">
            </div>
            <div class="title">
                Risk Factor Report
            </div>
            <div class="date">
                March 16, 2022 || 15:00:00
            </div>
        </div>
        <div class="content">
            <div class="qn"> 1. What is your current stage of life? </div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 2. How familiar are you with financial markets? </div>
            <div class="ans"> Ans: Have basic knowledge & a little experience</div>
            <div class="qn"> 3. What is your purpose of investing?</div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 4. Your current portfolio consists of : </div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 5. How long do you plan to keep the investments? </div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 6. How much liquidity you want to maintain? </div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 7. How long will you hold on to an investment which is not doing well? </div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 8. If the Investment value goes down by 25 % though the fundamentals have not changed, you will </div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 9. What type of loss you are willing to book or accept at any point of time ?</div>
            <div class="ans"> Ans: Single </div>
            <div class="qn"> 10. Which of the followings describes you the best?</div>
            <div class="ans"> Ans: Single </div>
        </div>
        <div class="title" id="bt">
            Risk Factor Report
        </div>
        <div class="bottom">
            <div class="metre">
                <img src="bad.png" alt="" id="img1">
                <!-- <img src="niddle.png" alt="" srcset="" id="img2">
                <h2 id="txt">Performance Report : 80% </h2> -->
            </div>
            <div class="report">
                <h2>You have scored 90 out of 100</h2>
                <h3>Based on your score below is the suggestion</h3>
                <h5>Lorem Ipsum is simply dummy text of the printing
                    and typesetting industry. Lorem Ipsum has been
                    the industry's standard dummy text ever since the
                    1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type.</h5>
            </div>
        </div>
    </div>
    <div class="footer">
        www.stocktick.com
    </div>
</body>
</html>"""
pdfkit.from_string(k,'test.pdf')