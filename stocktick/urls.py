"""stocktick URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
# from django.contrib import admin
# from django.urls import path
#
# urlpatterns = [
#     path('admin/', admin.site.urls),
# ]
from django.contrib import admin
from django.urls import include, path
from rest_framework import routers
from users import views
from users.views import FileUploader
from services import views1

router = routers.DefaultRouter()
# router.register(r'users', views.UserViewSet)
# router.register(r'groups', views.GroupViewSet)

# Wire up our API using automatic URL routing.
# Additionally, we include login URLs for the browsable API.
urlpatterns = [
    path('', include(router.urls)),
    path('admin/', admin.site.urls),
    path('post/contactus/', views1.addContactUsDetails),
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),
    path('get/all/otp/', views.getAllOTP),
    path('auth/getotp/', views.GetOtp),
    path('auth/validateotp/', views.validateOtp),
    path('update/userinfo/', views.updateUserInfo),
    path('get/userinfo/', views.getUserInfo),
    path('updateuser/', views.UpdateUserAPIView.as_view()),
    path('get/loans/', views1.getLoans),
    path('get/headlines/', views1.getheadlines),
    path('get/Insurance/', views1.getInsurance),
    path('get/education/', views1.getEducation),
    path('get/media/', views1.getMedia),
    path('add/loandetails/', views1.addLoanDetails),
    path('add/insurance/details/', views1.addInsuranceDetails),
    path('get/webinars/', views1.getWebinar),
    path('subscribe/webinar/', views1.subscribeWebinar),
    path('blogs/', include('blogs.urls')),
    path('sms/', include('smsreading.urls')),
    path('riskfactor/', include('riskFactor.urls')),
    path('finantialplanning/', include('finantialPlanning.urls')),
    path('getassistance/', include('GetAssistance.urls')),
    path('discover-mutual-funds/', include('DiscoverMutualFunds.urls')),
    path('fileUploader', FileUploader.as_view()),
    path('asset-recorder/', include('assetRecorder.urls')),
]
