"""
Django settings for stocktick project.

Generated by 'django-admin startproject' using Django 4.0.1.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

from pathlib import Path
import os
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

ACCESS_KEY = "********************"
SECRET_ACCESS_KEY = "KPHNCVWnsqMHMzGGcAarLQDrwOyF7Vb2SfXrKyh0"

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-tu7gc8v%^2vwm0=mp&h#c0!+(+ullj(ujjqat-8v#kcw+x-_s('

# SECURITY WARNING: don't run with debug turned on in production!

DEBUG = True

ALLOWED_HOSTS = ['*']

CORS_ORIGIN_ALLOW_ALL = True

# ALLOWED_HOSTS = [
#     '*', 'http://localhost:3000', 'https://stocktick.in', 'https://www.stocktick.in']
#
# #CORS_ORIGIN_ALLOW_ALL = True
# CORS_ALLOWED_ORIGINS = [
#     "https://stocktick.in", "https://api.stocktick.in", "https://www.stocktick.in", "http://127.0.0.1:80", "http://127.0.0.1:443", "https://www.api.stocktick.in", "http://*************"
# ]
# #CORS_ALLOWED_ORIGINS = ['*']
#
# CORS_ALLOW_METHODS = [
#     'GET',
#     'OPTIONS',
#     'POST'
# ]

# Application definition

INSTALLED_APPS = [
    'django_crontab',
    'corsheaders',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'users.apps.UsersConfig',
    'services.apps.ServicesConfig',
    'blogs.apps.BlogsConfig',
    'smsreading.apps.SmsreadingConfig',
    'riskFactor.apps.riskFactorConfig',
    'finantialPlanning.apps.FinantialplanningConfig',
    'GetAssistance.apps.GetAssistanceConfig',
    'DiscoverMutualFunds.apps.DMFConfig',
    'assetRecorder.apps.assetconfig',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'stocktick.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'stocktick.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'stocktick-qa',
        'USER': 'admin',
        'PASSWORD': 'stocktickanamdc',
        'HOST': 'stocktick-qa.cdkk5gjuaasl.ap-south-1.rds.amazonaws.com',
        'PORT': '3306',
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10
}


# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

# STATIC_URL = '/static/'
# PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))
# STATIC_ROOT = os.path.join(PROJECT_DIR, 'static')
# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = 'mJ9mOQtZA0602SCijb95b+5l/lLqq//IhYG1xdOj'
AWS_STORAGE_BUCKET_NAME = 'stocktick1'
AWS_S3_CUSTOM_DOMAIN = '%s.s3.amazonaws.com' % AWS_STORAGE_BUCKET_NAME
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',
}
AWS_STATIC_LOCATION = 'static'
AWS_IMAGE_LOCATION = 'images'

STATIC_URL = 'https://%s/%s/' % (AWS_S3_CUSTOM_DOMAIN, AWS_STATIC_LOCATION)
STATICFILES_STORAGE = 'stocktick.storage_backends.StaticStorage'
DEFAULT_FILE_STORAGE = 'stocktick.storage_backends.MediaStorage'


CSRF_COOKIE_SECURE = True
CSRF_TRUSTED_ORIGINS = ['https://*.stocktick.in']

SMTP_EMAIL = '<EMAIL>'
SMTP_PASSWORD = 'Stocktick@2022'

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST ='smtp.yahoo.fr'
EMAIL_USE_TLS = True
EMAIL_PORT = 587
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'Stocktick@2022'

CRONJOBS = [
    ('* * * * *', 'stocktick.cron.syncRates','>> /file.log')
]