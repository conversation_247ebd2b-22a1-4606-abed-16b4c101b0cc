from rest_framework import serializers

from .models import RiskQuestions,RiskUsers,RiskUsersResponses,RangeResult


class RiskQuestionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RiskQuestions
        fields = "__all__"

class RiskUsersSerializer(serializers.ModelSerializer):
    class Meta:
        model = RiskUsers
        fields = "__all__"

class RiskUsersResponsesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RiskUsersResponses
        fields = "__all__"

class RangeResultSerializer(serializers.ModelSerializer):
    class Meta:
        model = RangeResult
        fields = "__all__"