from django.contrib import admin
from .models import *

@admin.register(RiskQuestions)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'questions','status')


@admin.register(RangeResult)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'value1', 'value2','risk_profile')


@admin.register(RiskUsers)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'user_id','status')


@admin.register(RiskUsersResponses)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'User_id','score_sum')