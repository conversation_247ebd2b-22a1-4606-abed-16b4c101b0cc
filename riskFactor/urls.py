from django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
# from rest_framework.routers import DefaultRouterfrom django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
from rest_framework.routers import DefaultRouter


from .views import RiskUserAPIView, RiskQuestionsAPIView,RiskUserResponsesAPIView,RangeResultAPIView,RiskReportPdf


urlpatterns = [
    # path('article/', article_list), # Function based calls
    path('user/', RiskUserAPIView.as_view()),
    path('questions/', RiskQuestionsAPIView.as_view()),
    path('response/', RiskUserResponsesAPIView.as_view()),
    path('result/', RangeResultAPIView.as_view()),
    path('pdf/',RiskReportPdf.as_view())
]
