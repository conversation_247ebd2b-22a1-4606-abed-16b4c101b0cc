CSS = """
    <style>
        .header {
            display: flex;
            flex-direction: row;
            width: 100%;
        }

        .title {
            font-size: 22px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            background-color: rgb(99, 124, 40);
            padding: 2px;
            width: 300px;
            height: 40px;
            text-align: center;
            border-radius: 3px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            transform: translateX(50px);
            margin-left: auto;
            margin-right: auto;
        }

        .date {
            font-size: 12px;
            width: 100px;

        }

        .content {
            display: flex;
            flex-direction: column;
            width: 100%;
            margin-top: 35px;
        }

        .qn {
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-color: rgb(221, 222, 223);
            color: rgb(56, 56, 56);
            border-radius: 3px;
        }

        .ans {
            padding: 3px;
            font-size: 14px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            background-color: rgb(239, 239, 239);
            color: rgb(56, 56, 56);
            border-radius: 3px;
            margin-top: 3px;
            margin-bottom: 3px;
        }

        #bt {
            margin: 20px;
        }

        .footer {
            background-color: rgb(221, 222, 223);
            width: 100%;
            padding: 10px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            text-align: center;
            font-size: 20px;
        }

        .bottom {
            text-align: center;
            margin-left: auto;
            margin-right: auto;
        }

        .report {
            width: 100%;
            margin: 5px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            font-weight: bold;
            text-align: center;
        }

        .report h3 {
            margin-top: -18px;
        }


        #img1 {
            width: 80%;
            position: relative;
        }

        #img2 {
            width: 15%;
            position: absolute;
            transition: translatex(3px);
            left: 22%;
            bottom: 42%;
        }


        .metre {
            width: 45%;
            margin-top: -48px;
            text-align: center;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        .logo {
            width: 100px;
            height: 50px;
            height: auto;
            float: left;
            margin: 3px;
            padding: 3px;
            margin-top: -10px;
        }

        #txt {
            font-size: 26px;
            position: absolute;
            top: 55%;
            left: 74px;
        }

        .row {
            display: flex;
            margin-bottom: 10px;
        }

        .column1 {
            flex: 50%;
            padding: 10px;
            height: 50px;
        }
        .column2 {
            flex: 50%;
            padding: 10px;
            height: 50px;
        }
        .column3 {
            flex: 30%;
            padding: 10px;
            height: 50px;
            text-align: center;

        }
    </style>
"""