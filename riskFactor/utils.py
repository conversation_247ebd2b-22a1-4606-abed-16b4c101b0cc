import json
# Create your views here.
from datetime import date, datetime
import pytz

import random
from uuid import uuid4

# import smtplib
# from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
# from email.mime.text import MIMEText
# import smtplib
# import ssl
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from django.core.mail import send_mail


from .models import RiskQuestions, RiskUsers, RangeResult, RiskUsersResponses
from .serializers import RangeResultSerializer, RiskUsersResponsesSerializer, RiskUsersSerializer, \
    RiskQuestionsSerializer

from users.constant import ACTIVE
from .css import CSS
import pdfkit
import os
from .models import *
import boto3
from django.conf import settings

s3 = boto3.client(
    's3',
    aws_access_key_id=settings.ACCESS_KEY,
    aws_secret_access_key=settings.SECRET_ACCESS_KEY,
)


def getquestions(*val):
    categories = RiskQuestions.objects.filter(status=ACTIVE).values(*val)
    serializer = RiskQuestionsSerializer(categories, many=True)
    return serializer.data


def slugGenerator(inp):
    words = inp.split()
    number = random.randint(1000, 9999)
    words.append(str(number))
    return "-".join(words)


def tokenGenerator():
    return uuid4()


def generateoptions(scorelist1):
    quesdata = getquestions()
    opt=[]
    for i in range(len(quesdata)):
        optionNumber= "op"+str(scorelist1[i])
        opt.append(quesdata[i][optionNumber])
    return opt


def selectmeter(score):
    rangeRes = RangeResult.objects.filter(
        value1__lte=score, value2__gte=score)
    serializer = RangeResultSerializer(rangeRes, many=True)
    return serializer.data[0]['risk_profile']


def pdfGenerator(userId, score, allscores):
    scorelist1 = [allscores['score1'], allscores['score2'], allscores['score3'], allscores['score4'],
                  allscores['score5'], allscores['score6'], allscores['score7'], allscores['score8'],
                  allscores['score9'], allscores['score10'], ]

    questions = getquestions()
    opt = generateoptions(scorelist1)

    imgsc = selectmeter(score)
    imgsc = imgsc.replace(' ', '+')
    print(imgsc)
    # using simple format of showing time
    tz_IN = pytz.timezone('Asia/Kolkata')
    timecurr = datetime.now(tz_IN).strftime("%H:%M:%S")
    datecurr = datetime.today().date()

    # print(quesdata)

    html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css">
        <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.slim.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"></script>
        {CSS}
    </head>
    <body>
        <div class="container">
            <div class="row">
                <div class="column1">
                    <div class="logo">
                        <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/images/Logo+New.png" alt="" srcset="">
                    </div>
                </div>
                <div class="column2">
                    <div class="title">
                        Risk Profiler
                    </div>

                </div>

            </div>
            <div class="content">
                <div class="qn"> 1. {questions[0]['questions']} </div>
                <div class="ans"> Ans: {opt[0]} </div>
                <div class="qn"> 2. {questions[1]['questions']} </div>
                <div class="ans"> Ans: {opt[1]}</div>
                <div class="qn"> 3. {questions[2]['questions']}</div>
                <div class="ans"> Ans: {opt[2]} </div>
                <div class="qn"> 4. {questions[3]['questions']} </div>
                <div class="ans"> Ans: {opt[3]} </div>
                <div class="qn"> 5. {questions[4]['questions']}</div>
                <div class="ans"> Ans: {opt[4]} </div>
                <div class="qn"> 6. {questions[5]['questions']} </div>
                <div class="ans"> Ans: {opt[5]} </div>
                <div class="qn"> 7. {questions[6]['questions']} </div>
                <div class="ans"> Ans: {opt[6]} </div>
                <div class="qn"> 8. {questions[7]['questions']} </div>
                <div class="ans"> Ans: {opt[7]} </div>
                <div class="qn"> 9.{questions[8]['questions']}</div>
                <div class="ans"> Ans: {opt[8]} </div>
                <div class="qn"> 10. {questions[9]['questions']}</div>
                <div class="ans"> Ans: {opt[9]} </div>
            </div>
            <div class="bottom">
                <div class="metre">
                    <img src="https://stocktick1.s3.amazonaws.com/riskmeter/{imgsc}.png" alt="meterimage" id="img1">
                    <!-- <img src="niddle.png" alt="" srcset="" id="img2"-->
                    <!--h2 id="txt">Performance Report : {(score * 100) / 80}% </h2> -->
                </div>
                <div class="report">
                <h6>Your ability to take risks is evaluated by reviewing your assets and liabilities. An individual with many assets and few liabilities has a high ability to take on risk. Conversely, an individual with few assets and high liabilities has a low ability to take on risk. Also different phases of life affect the capacity to take risk </h6>
                    <h4>You have scored {score} out of 100</h4>
                    <h5>Based on your score below is the suggestion</h5>
                    <h6>Based on your score,  we have investment opportunities that suit your profile perfectly to provide peace of mind to attain your financial freedom journey. Get personalised advice to improve your profile and make it healthy today!</h6>
                </div>
            </div>
            <div class="footer">
                www.stocktick.in
            </div>
        </div>

    </body>
    </html>
    """
    print("I am inside4")

    file_name = f"riskFactor/{userId}.html"
    text_file = open(file_name, "w", encoding="utf-8")
    text_file.write(html)
    text_file.close()

    # path_wkhtmltopdf = r'C:/Program Files/wkhtmltopdf/bin/wkhtmltopdf.exe'
    # config = pdfkit.configuration(wkhtmltopdf=path_wkhtmltopdf)
    print("Krishna")
    file_read_from = f"./riskFactor/{userId}.html"
    filename = f"riskFactor/{userId}.pdf"
    try:
        pdfkit.from_file(file_read_from, filename)
    except Exception as e:
        print("Saving file as pdf failed")
        print(e)
        return 1
    return 1


def uploadFile(userId):
    filename = f"riskFactor/{userId}.pdf"
    uploadFileName = f"pdf/{userId}.pdf"
    fullFilePath = "https://stocktick1.s3.ap-south-1.amazonaws.com/" + uploadFileName
    try:
        s3.upload_file(filename, 'stocktick1', uploadFileName)
        try:
            cwd = os.path.dirname(__file__)
            os.remove(f"{cwd}/{userId}.html")
            os.remove(f"{cwd}/{userId}.pdf")
            print("files deleted")
        except Exception as e:
            print(e)
        return fullFilePath
    except Exception as e:  # getting permission denied error
        print(e)
        return False
    return True


def sendemail(to, sub, body):
    try:
        send_mail(
            'Subject here',
            'Here is the message.',
            '<EMAIL>',
            ['<EMAIL>'],
            fail_silently=False,
        )
    except Exception as e:
        print(e)
    # try:
    #     s = smtplib.SMTP(host='smtp-mail.outlook.com', port=587)
    #     s.starttls()
    #     s.login(settings.SMTP_EMAIL, settings.SMTP_PASSWORD)
    #     print(settings.SMTP_EMAIL)
    #     print(settings.SMTP_PASSWORD)
    #     msg = MIMEMultipart()       # create a message

    #     # add in the actual person name to the message template
    #     message = body
    #     print(to)

    #     # setup the parameters of the message
    #     msg['From']=settings.SMTP_EMAIL
    #     msg['To']=to
    #     msg['Subject']=sub

    #     # add in the message body
    #     msg.attach(MIMEText(message, 'plain'))

    #     # send the message via the server set up earlier.
    #     s.send_message(msg)

    #     del msg
    # except Exception as e:
    #     print(e)
# def sendemail(to, sub, body):
    # smtp_server = "smtp.gmail.com"
    # port = 587  # For starttls
    # sender_email = settings.SMTP_EMAIL
    # password = settings.SMTP_PASSWORD

    # # Create a secure SSL context
    # context = ssl.create_default_context()

    # # Try to log in to server and send email
    # try:
    #     server = smtplib.SMTP(smtp_server, port)
    #     server.ehlo()  # Can be omitted
    #     server.starttls(context=context)  # Secure the connection
    #     server.ehlo()  # Can be omitted
    #     server.login(sender_email, password)
    #     server.sendmail(sender_email, to, body)
    #     # TODO: Send email here
    # except Exception as e:
    #     # Print any error messages to stdout
    #     print(e)
    # finally:
    #     server.quit()
    # sender = '<EMAIL>'
    # receivers = ['<EMAIL>']

    # <AUTHOR> <EMAIL>
    # <AUTHOR> <EMAIL>
    # Subject: SMTP e-mail test

    # This is a test e-mail message.
    # """

    # try:
    #     smtpObj = smtplib.SMTP('localhost')
    #     smtpObj.sendmail(sender, receivers, message)
    #     print ("Successfully sent email")
    # except Exception as e:
    #     print ("Error: unable to send email")
    #     print(e)
