from distutils.util import execute
import json
from os import remove
from urllib import response

from users.auth import verifyUser

# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
# from itsdangerous import Serializer
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings

from uuid import uuid4
import pytz
import datetime
from .models import RiskQuestions, RiskUsers, RangeResult, RiskUsersResponses

from .utils import tokenGenerator, getquestions, pdfGenerator, uploadFile
from users.constant import ROW_STATUS, ACTIVE

from .serializers import RangeResultSerializer, RiskUsersResponsesSerializer, RiskUsersSerializer, \
    RiskQuestionsSerializer


class RiskUserAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            users = RiskUsers.objects.filter(user_id=userId)
            users = RiskUsersSerializer(users, many=True)
            return Response(users.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        name = data["name"]
        email = data["email"]
        age = data["age"]
        gender = data["gender"]
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            exists = RiskUsers.objects.get(user_id=userId)
        except:
            exists = -1
        print(exists)
        if (exists != -1):

            exists.modified_by = userId
            exists.name = name
            exists.email = email
            exists.age = age
            exists.gender = gender
            exists.active = True
            exists.save()
            return Response({"message": "User details Updated"}, status=status.HTTP_201_CREATED)

        else:
            if data:
                data["created_by"] = userId
                data["modified_by"] = userId
                data["user_id"] = userId

            serializer = RiskUsersSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RiskQuestionsAPIView(APIView):
    def get(self, request):
        ques = getquestions('id', 'questions', 'op1',
                            'op2', 'op3', 'op4', 'op5', 'op6')
        return Response(ques)


class RiskUserResponsesAPIView(APIView):
    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        questions = getquestions()
        dicn = dict({'1': 'sc1', '2': 'sc2', '3': 'sc3',
                     '4': 'sc4', '5': 'sc5', '6': 'sc6'})

        ans1 = questions[0][dicn[data['option1']]]
        ans2 = questions[1][dicn[data['option2']]]
        ans3 = questions[2][dicn[data['option3']]]
        ans4 = questions[3][dicn[data['option4']]]
        ans5 = questions[4][dicn[data['option5']]]
        ans6 = questions[5][dicn[data['option6']]]
        ans7 = questions[6][dicn[data['option7']]]
        ans8 = questions[7][dicn[data['option8']]]
        ans9 = questions[8][dicn[data['option9']]]
        ans10 = questions[9][dicn[data['option10']]]

        total = ans1 + ans2 + ans3 + ans4 + ans5 + ans6 + ans7 + ans8 + ans9 + ans10

        print("total", total)
        try:
            exists = RiskUsersResponses.objects.get(User_id=userId)
        except:
            exists = -1
        print(exists)
        if (exists != -1):
            # score now denots option numbers
            exists.modified_by = userId
            exists.score1 = data['option1']
            exists.score2 = data['option2']
            exists.score3 = data['option3']
            exists.score4 = data['option4']
            exists.score5 = data['option5']
            exists.score6 = data['option6']
            exists.score7 = data['option7']
            exists.score8 = data['option8']
            exists.score9 = data['option9']
            exists.score10 = data['option10']
            exists.score_sum = total
            exists.save()

            return Response({"message": "Response Updated"}, status=status.HTTP_201_CREATED)

        else:
            if data:
                data["created_by"] = userId
                data["modified_by"] = userId
                data["User_id"] = userId
                data["score_sum"] = total

            data['score1']=data['option1']
            data['score2']=data['option2']
            data['score3']=data['option3']
            data['score4']=data['option4']
            data['score5']=data['option5']
            data['score6']=data['option6']
            data['score7']=data['option7']
            data['score8']=data['option8']
            data['score9']=data['option9']
            data['score10']=data['option10']

            serial = RiskUsersResponsesSerializer(data=data)
            if serial.is_valid():
                serial.save()
                return Response({"message": "Response Updated"}, status=status.HTTP_201_CREATED)
        return Response(serial.errors, status=status.HTTP_400_BAD_REQUEST)


class RangeResultAPIView(APIView):
    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        total = RiskUsersResponses.objects.filter(User_id=userId)
        serializer1 = RiskUsersResponsesSerializer(total, many=True)
        score = serializer1.data[0]['score_sum']

        rangeRes = RangeResult.objects.filter(
            value1__lte=score, value2__gte=score)
        serializer = RangeResultSerializer(rangeRes, many=True)
        return Response({"range": serializer.data, "total score": score}, status=status.HTTP_201_CREATED)


class RiskReportPdf(APIView):
    def get(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        if not token:
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        total = RiskUsersResponses.objects.filter(User_id=userId)
        serializer1 = RiskUsersResponsesSerializer(total, many=True)

        if (serializer1.data == []):
            return Response({"message": "Data Not Found"}, status=status.HTTP_404_NOT_FOUND)
        score = serializer1.data[0]['score_sum']
        print("I am here dost.. check me out")
        print(score,serializer1.data[0])
        flg = pdfGenerator(userId, score, serializer1.data[0])
        k = False
        k = uploadFile(userId)
        print(k)
        if flg == 1 and k:
            return Response({"message": "Report Generated and Uploaded", "url": k}, status=status.HTTP_201_CREATED)
        elif flg == 1 and not k:
            return Response({"message": "Report Generated but uploading file failed"}, status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({"message": "Report Not Generated"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
