# Generated by Django 4.0.3 on 2022-03-11 05:04

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RangeResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('value1', models.IntegerField(unique=True)),
                ('value2', models.IntegerField(unique=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
            ],
            options={
                'db_table': 'tbl_range_result',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RiskQuestions',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('sno', models.AutoField(primary_key=True, serialize=False)),
                ('questions', models.TextField(max_length=500)),
                ('op1', models.TextField(blank=True, max_length=100, null=True)),
                ('sc1', models.IntegerField(default=0)),
                ('op2', models.TextField(blank=True, max_length=100, null=True)),
                ('sc2', models.IntegerField(default=0)),
                ('op3', models.TextField(blank=True, max_length=100, null=True)),
                ('sc3', models.IntegerField(default=0)),
                ('op4', models.TextField(blank=True, max_length=100, null=True)),
                ('sc4', models.IntegerField(default=0)),
                ('op5', models.TextField(blank=True, max_length=100, null=True)),
                ('sc5', models.IntegerField(default=0)),
                ('op6', models.TextField(blank=True, max_length=100, null=True)),
                ('sc6', models.IntegerField(default=0)),
            ],
            options={
                'db_table': 'tbl_risk_questions',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RiskUsers',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.TextField()),
                ('email', models.EmailField(max_length=100)),
                ('age', models.IntegerField()),
                ('gender', models.CharField(max_length=6)),
                ('token', models.TextField(unique=True)),
            ],
            options={
                'db_table': 'tbl_risk_users',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RiskUsersResponses',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('token', models.TextField(unique=True)),
                ('score1', models.IntegerField(default=0)),
                ('score2', models.IntegerField(default=0)),
                ('score3', models.IntegerField(default=0)),
                ('score4', models.IntegerField(default=0)),
                ('score5', models.IntegerField(default=0)),
                ('score6', models.IntegerField(default=0)),
                ('score_sum', models.IntegerField(default=0)),
            ],
            options={
                'db_table': 'tbl_risk_users_responses',
                'managed': False,
            },
        ),
    ]
