from lib2to3.pgen2 import token
from math import fabs
from django.db import models
from users.constant import ROW_STATUS, ACTIVE

# Create your models here.
class RangeResult(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    value1 = models.IntegerField(unique=True, blank=False, null=False)
    value2 = models.IntegerField(unique=True, blank=False, null=False)
    description = models.TextField(max_length=500, blank=True, null=True)
    risk_profile = models.TextField(max_length=50, blank=True, null=True)

    def __str__(self):
        return str(self.value1) + "-" + str(self.value2)

    class Meta:
        managed = False
        db_table = 'tbl_range_result'


class RiskQuestions(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    questions = models.TextField(max_length=500, blank=False, null=False)
    op1 = models.TextField(max_length=100, blank=True, null=True)
    sc1 = models.IntegerField(default=0)
    op2 = models.TextField(max_length=100, blank=True, null=True)
    sc2 = models.IntegerField(default=0)
    op3 = models.TextField(max_length=100, blank=True, null=True)
    sc3 = models.IntegerField(default=0)
    op4 = models.TextField(max_length=100, blank=True, null=True)
    sc4 = models.IntegerField(default=0)
    op5 = models.TextField(max_length=100, blank=True, null=True)
    sc5 = models.IntegerField(default=0)
    op6 = models.TextField(max_length=100, blank=True, null=True)
    sc6 = models.IntegerField(default=0)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    def __str__(self):
        return str(self.questions)

    class Meta:
        managed = False
        db_table = 'tbl_risk_questions'


class RiskUsers(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    name = models.TextField(blank=False, null=False)
    email = models.EmailField(max_length=100, blank=False, null=False)
    age = models.IntegerField(blank=False, null=False)
    gender = models.CharField(max_length=6, blank=False, null=False)
    user_id = models.IntegerField(blank=False, unique=True, null=False)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    def __str__(self):
        return str(self.name)

    class Meta:
        managed = False
        db_table = 'tbl_risk_users'


class RiskUsersResponses(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    User_id = models.IntegerField(blank=False, null=False, unique=True)
    score1 = models.IntegerField(default=0)
    score2 = models.IntegerField(default=0)
    score3 = models.IntegerField(default=0)
    score4 = models.IntegerField(default=0)
    score5 = models.IntegerField(default=0)
    score6 = models.IntegerField(default=0)
    score7 = models.IntegerField(default=0)
    score8 = models.IntegerField(default=0)
    score9 = models.IntegerField(default=0)
    score10 = models.IntegerField(default=0)
    score_sum = models.IntegerField(default=20)

    def __str__(self):
        return str(self.User_id) + str(self.score_sum)

    def total(self):
        self.score_sum = self.score1 + self.score2 + self.score3 + self.score4 + self.score5 + self.score6 + self.score7 + self.score8 + self.score9 + self.score10
        super().total()

    class Meta:
        managed = False
        db_table = 'tbl_risk_users_responses'
