from django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
# from rest_framework.routers import DefaultRouterfrom django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
from rest_framework.routers import DefaultRouter

from .views import BlogCategoryAPIView, BlogKeywordAPIView, BlogCreationAPIView, BlogListingAPIView, BlogViewAPIView, BlogCommentAPIView, BlogRatingAPIView, BlogNewsletterAPIView

urlpatterns = [
    # path('article/', article_list), # Function based calls
    path('category/', BlogCategoryAPIView.as_view()),
    path('keyword/', BlogKeywordAPIView.as_view()),
    path('blog/create/',BlogCreationAPIView.as_view()),
    path('blog/<str:slug>/', BlogViewAPIView.as_view()),
    path('',BlogListingAPIView.as_view()),
    path('comment/',BlogCommentAPIView.as_view()),
    path('rating/',BlogRatingAPIView.as_view()),
    path('newsletter/',BlogNewsletterAPIView.as_view()),
    

]
