from django.contrib import admin
from .models import *

@admin.register(BlogRating)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'blog_id', 'user_id', 'like', 'status')

@admin.register(BlogBody)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'slug', 'title', 'status')


@admin.register(BlogCategory)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'status')


@admin.register(BlogComment)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'blog_id', 'user_id', 'status')


@admin.register(BlogKeyword)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'status')

@admin.register(BlogNewsletter)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'status')
