import json
# Create your views here.
from .models import Blog<PERSON>ategory, Blog<PERSON>eyword, BlogBody, BlogRating
from .serializers import BlogCategorySerializer, BlogKeywordSerializer, BlogRatingSerializer, BlogSerializer
from django.db import connection
import pytz
import datetime
import random
from .models import BlogCategory
from .serializers import BlogCategorySerializer
from users.constant import ACTIVE


def processKeywords(keywordsMap, keys):
    keys = keys.split("-")

    keywords = []
    for key in keys:
        title = keywordsMap.get(int(key), None)

        if title:
            temp = {}
            temp['id'] = key
            temp['title'] = title
            keywords.append(temp)
    return keywords


def processCategory(categoryMap, key):

    cat = {}
    cat['id'] = key
    cat['title'] = categoryMap.get(key,None)
    return cat


def formBlogs(blogs):
    categoriesMap = getCategoriesMap()
    keywordsMap = getKeywordsMap()
    for x in blogs:
        x['keyword_ids'] = processKeywords(keywordsMap, x["keyword_ids"])
        x['category_id'] = processCategory(categoriesMap, x["category_id"])

    return blogs


def getCategories():
    categories = BlogCategory.objects.filter(status=ACTIVE)
    serializer = BlogCategorySerializer(categories, many=True)
    return serializer.data


def getKeywords():
    categories = BlogKeyword.objects.filter(status=ACTIVE)
    serializer = BlogKeywordSerializer(categories, many=True)
    return serializer.data


def getCategoriesMap():
    cats = getCategories()
    categoriesMap = {}
    for xx in cats:
        categoriesMap[xx['id']] = xx['title']
    return categoriesMap


def getKeywordsMap():
    keywordsMap = {}
    keywords = getKeywords()
    for x in keywords:
        keywordsMap[x['id']] = x['title']
    return keywordsMap


def slugGenerator(inp):
    words = inp.split()
    number = random.randint(1000, 9999)
    words.append(str(number))
    return "-".join(words)
