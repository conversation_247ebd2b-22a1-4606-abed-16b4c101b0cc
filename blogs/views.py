from django.shortcuts import render
#from platformdirs import AppDirs
from rest_framework.views import APIView
from .models import Blog<PERSON>ategor<PERSON>, Blog<PERSON>eyword, BlogBody, BlogRating, BlogComment, BlogNewsletter
from .serializers import BlogCategorySerializer, BlogCommentSerializer, BlogKeywordSerializer, BlogNewsletterSerializer, BlogSerializer, BlogCommentSerializer, BlogRatingSerializer
from rest_framework.response import Response
from rest_framework import status
from .utils import slugGenerator, getCategories,formBlogs
from users.constant import ACTIVE

from rest_framework.permissions import IsAdminUser


class IsSuperUser(IsAdminUser):
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_superuser)


class BlogCategoryAPIView(APIView):
    permission_classes = (IsSuperUser,)

    def get(self, request):

        # categories = BlogCategory.objects.filter(active=True)
        # serializer = BlogCategorySerializer(categories, many=True)
        data=getCategories()
        return Response(data)

    def post(self, request):
        data = request.data
        usr = request.user
        if data:
            data["created_by"] = usr.id
            data["modified_by"] = usr.id
        serializer = BlogCategorySerializer(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BlogKeywordAPIView(APIView):
    permission_classes = (IsSuperUser,)

    def get(self, request):

        keywords = BlogKeyword.objects.filter(status=ACTIVE)
        serializer = BlogKeywordSerializer(keywords, many=True)
        return Response(serializer.data)

    def post(self, request):
        data = request.data
        usr = request.user
        if data:
            data["created_by"] = usr.id
            data["modified_by"] = usr.id
        serializer = BlogKeywordSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BlogCreationAPIView(APIView):
    permission_classes = (IsSuperUser,)

    def post(self, request):
        data = request.data
        usr = request.user
        if data:
            if data["slug"]:
                if BlogBody.objects.filter(slug=data["slug"]).exists():
                    return Response("Slug is already in use", status=status.HTTP_406_NOT_ACCEPTABLE)
            else:
                # Auto generate shrug
                slug = slugGenerator(data["title"])
                while BlogBody.objects.filter(slug=slug).exists():
                    slug = slugGenerator(data["title"])
                data["slug"] = slug
            data["created_by"] = usr.id
            data["modified_by"] = usr.id

            if data["keyword_ids"]:
                li = data["keyword_ids"]

                data["keyword_ids"] = "-".join(map(str, li))

        serializer = BlogSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BlogListingAPIView(APIView):

    def get(self, request):
        blogs = BlogBody.objects.filter(status=ACTIVE)
        serializer = BlogSerializer(blogs, many=True)
        blogd = serializer.data

        res = formBlogs(blogd)
        # blogd=KeyCatChange(blogd)

        return Response(res)


class BlogViewAPIView(APIView):

    def get_object(self, slug):
        try:
            return BlogBody.objects.get(slug=slug)
        except BlogBody.DoesNotExist:
            raise status.HTTP_404_NOT_FOUND

    def get(self, request, slug):
        print("SLUG is " + slug)
        try:
            blog = self.get_object(slug)
            serializer = BlogSerializer(blog)
        except:
            return Response({"msg":"No blog found with this slug"},status=status.HTTP_400_BAD_REQUEST)
        blogdata = serializer.data
        blogd = []
        blogd.append(blogdata)
        res = formBlogs(blogd)
        return Response(res[0])

class BlogCommentAPIView(APIView):

    def get(self, request):
        comments = BlogComment.objects.filter(status=ACTIVE)
        serializer = BlogCommentSerializer(comments, many=True)
        return Response(serializer.data)
    
    def post(self, request):
        data = request.data
        usr = request.user
        if data:
            data["created_by"] = usr.id
            data["modified_by"] = usr.id
        serializer = BlogCommentSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
class BlogRatingAPIView(APIView):

    def get(self, request):
        ratings = BlogRating.objects.filter(status=ACTIVE)
        serializer = BlogRatingSerializer(ratings, many=True)
        return Response(serializer.data)
    
    def post(self, request):
        data = request.data
        usr = request.user
        u_id = int(data["user_id"])
        b_id = int(data["blog_id"])
        if data:
            if BlogRating.objects.filter(blog_id=b_id, user_id=u_id).exists():
                return Response("already liked by user", status=status.HTTP_406_NOT_ACCEPTABLE)
            data["created_by"] = usr.id
            data["modified_by"] = usr.idS
        serializer = BlogRatingSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                

class BlogNewsletterAPIView(APIView):

    def get(self, request):
        emails = BlogNewsletter.objects.filter(status=ACTIVE)
        serializer = BlogNewsletterSerializer(emails, many=True)
        serializer = serializer.data
        return Response(serializer)
    
    def post(self, request):
        data = request.data
        usr = request.user
        if data:
            if BlogNewsletter.objects.filter(email_id=data["email_id"]).exists():
                return Response("email_id is already in use", status=status.HTTP_406_NOT_ACCEPTABLE)
            data["created_by"] = usr.id
            data["modified_by"] = usr.id
        serializer = BlogNewsletterSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

 

