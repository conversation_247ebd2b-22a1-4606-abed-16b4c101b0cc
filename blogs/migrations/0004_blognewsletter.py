# Generated by Django 4.0.2 on 2022-04-01 13:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blogs', '0003_blogcategory_delete_blogcategorie'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogNewsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('email_id', models.TextField(blank=True, max_length=1000, null=True)),
                ('user_id', models.IntegerField()),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.Char<PERSON><PERSON>(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'db_table': 'tbl_newsletter',
                'managed': False,
            },
        ),
    ]
