# Generated by Django 4.0.2 on 2022-02-26 18:59

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BlogBody',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('title', models.TextField(max_length=400, null=True)),
                ('short_desc', models.TextField(blank=True, max_length=1000, null=True)),
                ('featured_image_url', models.TextField(blank=True, max_length=250, null=True)),
                ('body', models.TextField(blank=True, null=True)),
                ('category_ids', models.TextField(blank=True, max_length=450, null=True)),
                ('keyword_ids', models.TextField(blank=True, max_length=450, null=True)),
                ('shrug', models.CharField(max_length=255, unique=True)),
            ],
            options={
                'db_table': 'tbl_blog_body',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogCategories',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(blank=True, default=datetime.date, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, default=datetime.date, null=True)),
                ('active', models.BooleanField(default=True)),
                ('title', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
                ('image_url', models.TextField(blank=True, max_length=450, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_categories',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogComments',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active', models.BooleanField(default=True)),
                ('blog_id', models.IntegerField()),
                ('user_id', models.IntegerField()),
                ('comment', models.TextField(blank=True, max_length=1000, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_comments',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogKeywords',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('title', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_keywords',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogRatings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active', models.BooleanField(default=True)),
                ('blog_id', models.IntegerField()),
                ('like', models.BooleanField(default=False)),
                ('dislike', models.BooleanField(default=False)),
                ('user_id', models.IntegerField()),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_ratings',
                'managed': False,
            },
        ),
    ]
