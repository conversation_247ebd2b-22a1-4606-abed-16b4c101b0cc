# Generated by Django 4.0.2 on 2022-02-26 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blogs', '0002_blogcategorie_blogcomment_blogkeyword_blograting_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('title', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
                ('image_url', models.TextField(blank=True, max_length=450, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_categories',
                'managed': False,
            },
        ),
        migrations.DeleteModel(
            name='BlogCategorie',
        ),
    ]
