# Generated by Django 4.0.2 on 2022-02-26 19:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blogs', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogCategorie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('title', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
                ('image_url', models.TextField(blank=True, max_length=450, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_categories',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active', models.BooleanField(default=True)),
                ('blog_id', models.IntegerField()),
                ('user_id', models.IntegerField()),
                ('comment', models.TextField(blank=True, max_length=1000, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_comments',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('title', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_keywords',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BlogRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active', models.BooleanField(default=True)),
                ('blog_id', models.IntegerField()),
                ('like', models.BooleanField(default=False)),
                ('dislike', models.BooleanField(default=False)),
                ('user_id', models.IntegerField()),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'db_table': 'tbl_blog_ratings',
                'managed': False,
            },
        ),
        migrations.DeleteModel(
            name='BlogCategories',
        ),
        migrations.DeleteModel(
            name='BlogComments',
        ),
        migrations.DeleteModel(
            name='BlogKeywords',
        ),
        migrations.DeleteModel(
            name='BlogRatings',
        ),
    ]
