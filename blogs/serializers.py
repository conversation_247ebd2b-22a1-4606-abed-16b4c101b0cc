from rest_framework import serializers

from .models import Blog<PERSON>ategor<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, BlogBody, BlogRating, BlogComment, BlogNewsletter


class BlogCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogCategory
        fields = "__all__"

class BlogKeywordSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogKeyword
        fields = "__all__"

class BlogSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogBody
        fields = "__all__"

class BlogCommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogComment
        fields = "__all__"

class BlogRatingSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogRating
        fields = "__all__"

class BlogNewsletterSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogNewsletter
        fields = "__all__"
