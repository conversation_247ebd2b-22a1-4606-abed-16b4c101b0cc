from django.db import models
from users.constant import ROW_STATUS, ACTIVE

# Create your models here.
class BlogCategory(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    title = models.CharField(max_length=45, blank=False, null=False)
    description = models.TextField(max_length=500, blank=True, null=True)
    image_url = models.TextField(max_length=450, blank=True, null=True)

    def __str__(self):
        return self.title

    class Meta:
        managed = False
        db_table = 'tbl_blog_categories'


class BlogKeyword(models.Model):
    created_by = models.Cha<PERSON><PERSON><PERSON>(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    title = models.CharField(max_length=45, blank=False, null=False)
    description = models.TextField(max_length=500, blank=True, null=True)

    def __str__(self):
        return self.title + " " + str(self.status)

    class Meta:
        managed = False
        db_table = 'tbl_blog_keywords'


class BlogBody(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    title = models.TextField(max_length=400, blank=False, null=True)
    short_desc = models.TextField(max_length=1000, blank=True, null=True)
    featured_image_url = models.TextField(max_length=250, blank=True, null=True)
    body = models.TextField(blank=True, null=True)
    category_id = models.IntegerField(blank=False, null=False, default=-1)
    keyword_ids = models.TextField(max_length=450, blank=True, null=True)
    slug = models.CharField(max_length=255, blank=False, unique=True)

    def __str__(self):
        return str(self.slug) + ": " + str(self.title)

    class Meta:
        managed = False
        db_table = 'tbl_blog_body'


class BlogRating(models.Model):
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    blog_id = models.IntegerField(blank=False, null=False)
    like = models.BooleanField(default=False, blank=False, null=False)
    dislike = models.BooleanField(default=False, blank=False, null=False)
    user_id = models.IntegerField(blank=False, null=False)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)

    def __str__(self):
        return str(self.blog_id) + str(self.like) + str(self.dislike)

    class Meta:
        managed = False
        db_table = 'tbl_blog_ratings'


class BlogComment(models.Model):
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    blog_id = models.IntegerField(blank=False, null=False)
    user_id = models.IntegerField(blank=False, null=False)
    comment = models.TextField(max_length=1000, blank=True, null=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)

    def __str__(self):
        return str(self.blog_id) + str(self.user_id)

    class Meta:
        managed = False
        db_table = 'tbl_blog_comments'

class BlogNewsletter(models.Model):
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    email_id = models.CharField(max_length=100, blank=False, unique=True)
    user_id = models.IntegerField(blank=False, null=False)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)

    def __str__(self):
        return str(self.user_id) + str(self.status)

    class Meta:
        managed = False
        db_table = 'tbl_newsletter'
