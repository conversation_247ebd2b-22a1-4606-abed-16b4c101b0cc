from django.db import models
from users.constant import ROW_STATUS, ACTIVE
# Create your models here.


class TblEducationService(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    midified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    active = models.CharField(max_length=45, blank=True, null=True)
    short_desc = models.CharField(max_length=45, blank=True, null=True)
    long_desc = models.CharField(max_length=350, blank=True, null=True)
    image_url = models.CharField(max_length=450, blank=True, null=True)
    video_link = models.CharField(max_length=450, blank=True, null=True)

    def __str__(self):
        return self.short_desc

    class Meta:
        managed = False
        db_table = 'tbl_education_service'


class TblInsuranceCar(models.Model):
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    status = models.CharField(max_length=45, blank=True, null=True)
    manufacer_date = models.DateField(blank=True, null=True)
    manufacturer = models.CharField(max_length=45, blank=True, null=True)
    current_policy_claim = models.CharField(
        max_length=1, blank=True, null=True)
    name = models.CharField(max_length=60, blank=True, null=True)
    email_id = models.CharField(max_length=85, blank=True, null=True)
    mobile = models.CharField(max_length=10, blank=True, null=True)
    vehicle_number = models.CharField(max_length=75, blank=True, null=True)
    model = models.CharField(max_length=45, blank=True, null=True)
    fuel_type = models.CharField(max_length=45, blank=True, null=True)
    variant = models.CharField(max_length=45, blank=True, null=True)
    current_insurer = models.CharField(max_length=45, blank=True, null=True)
    # Field name made lowercase.
    puc = models.CharField(db_column='PUC', max_length=1,
                           blank=True, null=True)
    # Field name made lowercase.
    puc_expiry = models.DateField(
        db_column='PUC_expiry', blank=True, null=True)
    month_12_ownership = models.CharField(max_length=1, blank=True, null=True)
    policy_expiry_date = models.DateField(blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        managed = False
        db_table = 'tbl_insurance_car'


class TblInsuranceHealth(models.Model):
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    status = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=45, blank=True, null=True)
    location = models.CharField(max_length=70, blank=True, null=True)
    email = models.CharField(max_length=70, blank=True, null=True)
    mobile = models.CharField(max_length=10, blank=True, null=True)
    age = models.IntegerField(blank=True, null=True)
    insurance_for = models.CharField(max_length=45, blank=True, null=True)
    insurer_name = models.CharField(max_length=45, blank=True, null=True)
    insurer_age = models.CharField(max_length=45, blank=True, null=True)
    medical_condition = models.CharField(max_length=45, blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        managed = False
        db_table = 'tbl_insurance_health'


class TblInsuranceLife(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=60, blank=True, null=True)
    location = models.CharField(max_length=305, blank=True, null=True)
    email = models.CharField(max_length=85, blank=True, null=True)
    mobile = models.CharField(max_length=45, blank=True, null=True)
    dob = models.DateField(blank=True, null=True)
    cover_life = models.CharField(max_length=60, blank=True, null=True)
    cover_upto = models.DateField(blank=True, null=True)
    amount = models.CharField(max_length=90, blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        managed = False
        db_table = 'tbl_insurance_life'


class TblInsuranceService(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True)
    active = models.CharField(max_length=45, blank=True, null=True)
    link = models.CharField(max_length=450, blank=True, null=True)
    short_desc = models.CharField(max_length=45, blank=True, null=True)
    long_desc = models.CharField(max_length=350, blank=True, null=True)
    img_urls = models.CharField(max_length=450, blank=True, null=True)
    category = models.CharField(max_length=45, blank=True, null=True)
    video_links = models.CharField(max_length=450, blank=True, null=True)
    min_score = models.IntegerField(blank=True, null=True)
    max_score = models.IntegerField(blank=True, null=True)

    def __str__(self):
        return self.short_desc

    class Meta:
        managed = False
        db_table = 'tbl_insurance_service'


class TblLoanDetails(models.Model):
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    status = models.CharField(max_length=8, blank=True, null=True)
    type_loan = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=85, blank=True, null=True)
    email = models.CharField(max_length=90, blank=True, null=True)
    phone = models.CharField(max_length=10, blank=True, null=True)
    business_name = models.CharField(max_length=200, blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        managed = False
        db_table = 'tbl_loan_details'


class TblLoanService(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_date = models.DateTimeField(blank=True, null=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_date = models.DateTimeField(blank=True, null=True)
    active = models.CharField(max_length=45, blank=True, null=True)
    link = models.CharField(max_length=450, blank=True, null=True)
    short_desc = models.CharField(max_length=45, blank=True, null=True)
    long_desc = models.CharField(max_length=350, blank=True, null=True)
    image_urls = models.CharField(max_length=245, blank=True, null=True)
    image_urls_phone = models.CharField(max_length=245, blank=True, null=True)
    category = models.CharField(max_length=45, blank=True, null=True)
    interest = models.FloatField(blank=True, null=True)
    color_code = models.CharField(max_length=45, blank=True, null=True)
    min_score = models.IntegerField(blank=True, null=True)
    max_score = models.IntegerField(blank=True, null=True)

    def __str__(self):
        return self.short_desc

    class Meta:
        managed = False
        db_table = 'tbl_loan_service'


class TblMediaService(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True)
    active = models.CharField(max_length=45, blank=True, null=True)
    short_desc = models.CharField(max_length=45, blank=True, null=True)
    long_desc = models.CharField(max_length=350, blank=True, null=True)
    image_url = models.CharField(max_length=450, blank=True, null=True)
    video_link = models.CharField(max_length=450, blank=True, null=True)
    media_post_date = models.DateField(blank=True, null=True)

    def __str__(self):
        return self.short_desc

    class Meta:
        managed = False
        db_table = 'tbl_media_service'

class TblHeadlineService(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    page = models.CharField(max_length=2, blank=False, null=False)
    headline = models.TextField(max_length=1000, blank=True, null=True)
    platform = models.CharField(max_length=1, blank=False, null=False)

    def __str__(self):
        return self.short_desc

    class Meta:
        managed = False
        db_table = 'tbl_headlines'
