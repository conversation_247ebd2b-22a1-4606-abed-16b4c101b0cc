# Generated by Django 4.0.4 on 2022-06-09 09:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TblHeadlineService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('page', models.Char<PERSON><PERSON>(max_length=2)),
                ('headline', models.TextField(blank=True, max_length=1000, null=True)),
                ('platform', models.CharField(max_length=1)),
            ],
            options={
                'db_table': 'tbl_headlines',
                'managed': False,
            },
        ),
    ]
