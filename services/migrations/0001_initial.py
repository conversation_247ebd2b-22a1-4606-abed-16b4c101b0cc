# Generated by Django 4.0.1 on 2022-02-25 18:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TblEducationService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('midified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, null=True)),
                ('active', models.CharField(blank=True, max_length=45, null=True)),
                ('short_desc', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
                ('long_desc', models.Char<PERSON>ield(blank=True, max_length=350, null=True)),
                ('image_url', models.Char<PERSON><PERSON>(blank=True, max_length=450, null=True)),
                ('video_link', models.Char<PERSON>ield(blank=True, max_length=450, null=True)),
            ],
            options={
                'db_table': 'tbl_education_service',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblInsuranceCar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('status', models.CharField(blank=True, max_length=45, null=True)),
                ('manufacer_date', models.DateField(blank=True, null=True)),
                ('manufacturer', models.CharField(blank=True, max_length=45, null=True)),
                ('current_policy_claim', models.CharField(blank=True, max_length=1, null=True)),
                ('name', models.CharField(blank=True, max_length=60, null=True)),
                ('email_id', models.CharField(blank=True, max_length=85, null=True)),
                ('mobile', models.CharField(blank=True, max_length=10, null=True)),
                ('vehicle_number', models.CharField(blank=True, max_length=75, null=True)),
                ('model', models.CharField(blank=True, max_length=45, null=True)),
                ('fuel_type', models.CharField(blank=True, max_length=45, null=True)),
                ('variant', models.CharField(blank=True, max_length=45, null=True)),
                ('current_insurer', models.CharField(blank=True, max_length=45, null=True)),
                ('puc', models.CharField(blank=True, db_column='PUC', max_length=1, null=True)),
                ('puc_expiry', models.DateField(blank=True, db_column='PUC_expiry', null=True)),
                ('month_12_ownership', models.CharField(blank=True, max_length=1, null=True)),
                ('policy_expiry_date', models.DateField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tbl_insurance_car',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblInsuranceHealth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('status', models.CharField(blank=True, max_length=45, null=True)),
                ('name', models.CharField(blank=True, max_length=45, null=True)),
                ('location', models.CharField(blank=True, max_length=70, null=True)),
                ('email', models.CharField(blank=True, max_length=70, null=True)),
                ('mobile', models.CharField(blank=True, max_length=10, null=True)),
                ('age', models.IntegerField(blank=True, null=True)),
                ('insurance_for', models.CharField(blank=True, max_length=45, null=True)),
                ('insurer_name', models.CharField(blank=True, max_length=45, null=True)),
                ('insurer_age', models.CharField(blank=True, max_length=45, null=True)),
                ('medical_condition', models.CharField(blank=True, max_length=45, null=True)),
            ],
            options={
                'db_table': 'tbl_insurance_health',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblInsuranceLife',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(blank=True, max_length=45, null=True)),
                ('name', models.CharField(blank=True, max_length=60, null=True)),
                ('location', models.CharField(blank=True, max_length=305, null=True)),
                ('email', models.CharField(blank=True, max_length=85, null=True)),
                ('mobile', models.CharField(blank=True, max_length=45, null=True)),
                ('dob', models.DateField(blank=True, null=True)),
                ('cover_life', models.CharField(blank=True, max_length=60, null=True)),
                ('cover_upto', models.DateField(blank=True, null=True)),
                ('amount', models.CharField(blank=True, max_length=90, null=True)),
            ],
            options={
                'db_table': 'tbl_insurance_life',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblInsuranceService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, null=True)),
                ('active', models.CharField(blank=True, max_length=45, null=True)),
                ('link', models.CharField(blank=True, max_length=450, null=True)),
                ('short_desc', models.CharField(blank=True, max_length=45, null=True)),
                ('long_desc', models.CharField(blank=True, max_length=350, null=True)),
                ('img_urls', models.CharField(blank=True, max_length=450, null=True)),
                ('category', models.CharField(blank=True, max_length=45, null=True)),
                ('video_links', models.CharField(blank=True, max_length=450, null=True)),
                ('min_score', models.IntegerField(blank=True, null=True)),
                ('max_score', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tbl_insurance_service',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblLoanDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('status', models.CharField(blank=True, max_length=8, null=True)),
                ('type_loan', models.CharField(blank=True, max_length=45, null=True)),
                ('name', models.CharField(blank=True, max_length=85, null=True)),
                ('email', models.CharField(blank=True, max_length=90, null=True)),
                ('phone', models.CharField(blank=True, max_length=10, null=True)),
                ('business_name', models.CharField(blank=True, max_length=200, null=True)),
            ],
            options={
                'db_table': 'tbl_loan_details',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblLoanService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_date', models.DateTimeField(blank=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_date', models.DateTimeField(blank=True, null=True)),
                ('active', models.CharField(blank=True, max_length=45, null=True)),
                ('link', models.CharField(blank=True, max_length=450, null=True)),
                ('short_desc', models.CharField(blank=True, max_length=45, null=True)),
                ('long_desc', models.CharField(blank=True, max_length=350, null=True)),
                ('image_urls', models.CharField(blank=True, max_length=245, null=True)),
                ('category', models.CharField(blank=True, max_length=45, null=True)),
                ('interest', models.FloatField(blank=True, null=True)),
                ('color_code', models.CharField(blank=True, max_length=45, null=True)),
                ('min_score', models.IntegerField(blank=True, null=True)),
                ('max_score', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tbl_loan_service',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TblMediaService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('midified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(blank=True, null=True)),
                ('active', models.CharField(blank=True, max_length=45, null=True)),
                ('short_desc', models.CharField(blank=True, max_length=45, null=True)),
                ('long_desc', models.CharField(blank=True, max_length=350, null=True)),
                ('image_url', models.CharField(blank=True, max_length=450, null=True)),
                ('video_link', models.CharField(blank=True, max_length=450, null=True)),
                ('media_post_date', models.DateField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tbl_media_service',
                'managed': False,
            },
        ),
    ]
