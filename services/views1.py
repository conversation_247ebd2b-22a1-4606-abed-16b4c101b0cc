from django.shortcuts import render
import json
# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings

# from riskFactor.utils import sendemail
from .serializer import MediaSerializer, HeadlineSerializer
from .models import TblMediaService, TblHeadlineService
from django.views.decorators.csrf import csrf_exempt
from uuid import uuid4
import pytz
import datetime

# from django.core.mail import send_mail


def getTokenExpiryTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    now = datetime.datetime.now(IST) + datetime.timedelta(days=30)
    print(now)
    return str(now)[:-6]


def getCurrentTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    return str(datetime.datetime.now(IST))[:-6]
# Create your views here.


def verifyUser(token):
    conn = connection
    cur = conn.cursor()

    now = getCurrentTimestamp()
    token_expiry = getTokenExpiryTimestamp()
    q = "SELECT user_id,token_expiry FROM tbl_user_sessions WHERE token = '%s'" % token
    cur.execute(q)
    res = cur.fetchone()
    print(q)

    if res:
        # USER_ID = res['id']
        if str(res[1]) < now:
            cur.close()
            # conn.close()
            return {"message": False, "reason": "Token Expired"}

        USER_ID = res[0]
        update = "UPDATE tbl_user_sessions SET token_last_used = '%s', token_expiry = '%s' WHERE user_id = %s " % (
            now, token_expiry, res[0])
        cur.execute(update)
        conn.commit()
        print(update)
        cur.close()
        # conn.close()

        return {"message": True, "id": res[0], "reason": "Success"}
    else:
        cur.close()
        # conn.close()
        return {"message": False, "reason": "Token Not Found"}


@api_view(["GET"])
@csrf_exempt
def getLoans(req):
    try:
        params = "W"
        try:
            # print(token)
            params = req.headers["platform"]

        except Exception as e:
            try:
                print("Here")
                params = req.query_params['platform']

            except Exception as e:
                return Response({"message": "Required parameters not found in the request"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        if params == "M":
            qq = "SELECT link,short_desc,long_desc,image_urls_phone,category,interest,color_code,min_score,max_score FROM tbl_loan_service"
        else:
            qq = "SELECT link,short_desc,long_desc,image_urls,category,interest,color_code,min_score,max_score FROM tbl_loan_service"

        cur.execute(qq)
        res = cur.fetchall()
        lis = []
        for r in res:
            lis.append({"link": r[0], "short_desc": r[1], "long_desc": r[2], "image_url": r[3],
                       "category": r[4], "interest": r[5], "color_code": r[6], "min_score": r[7], "max_score": r[8]})

        return JsonResponse(lis, safe=False)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
def getheadlines(req):
    try:
        page = "H"
        platform = "W"
        try:
            # print(token)
            page = req.headers["page"]
            platform = req.headers["platform"]

        except Exception as e:
            try:
                page = req.query_params['page']
                platform = req.query_params['platform']

            except Exception as e:
                return Response({"message": "Required parameters not found in the request"}, status=status.HTTP_404_NOT_FOUND)

        try:
            res = TblHeadlineService.objects.get(page=page, platform=platform)
            serializer = HeadlineSerializer(res)
            return Response(serializer.data)
        except Exception as e:
            print(e.args)
            return Response({"message": "No headline found with the combinations"}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
def getInsurance(req):
    try:
        try:
            platform = req.headers["platform"]

        except Exception as e:
            try:
                platform = req.query_params['platform']

            except Exception as e:
                return Response({"message": "Required parameters not found in the request"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()
        if platform == "M":
            qq = "SELECT link,short_desc,long_desc,img_urls,category,video_links,min_score,max_score FROM `stocktick-qa`.tbl_insurance_service LIMIT 3"
        else:
            qq = "SELECT link,short_desc,long_desc,img_urls,category,video_links,min_score,max_score FROM `stocktick-qa`.tbl_insurance_service"
        cur.execute(qq)
        res = cur.fetchall()

        lis = []
        for r in res:
            lis.append({"link": r[0], "short_desc": r[1], "long_desc": r[2], "image_url": r[3],
                       "category": r[4], "video_links": r[5], "min_score": r[6], "max_score": r[7]})
        return JsonResponse(lis, safe=False)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
def getEducation(req):
    try:
        # try:
        #     token = req.headers["authToken"]
        #     # print(token)
        #     response = verifyUser(token)
        #     if response["message"] is True:
        #         user_id = response['id']
        #         print(user_id, 'hola')
        #     else:
        #         return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        # except Exception as e:
        #     try:
        #         token = req.query_params['authToken']
        #         response = verifyUser(token)
        #         if response["message"] is True:
        #             user_id = response['id']
        #             print(user_id, 'hola')
        #         else:
        #             return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        #     except Exception as e:
        #         print(e.args)
        #         return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        qq = "SELECT short_desc,long_desc,image_url,video_link,blog_link FROM `stocktick-qa`.tbl_education_service"

        cur.execute(qq)
        res = cur.fetchall()

        lis = []
        for r in res:
            lis.append(
                {"short_desc": r[0], "long_desc": r[1], "image_url": r[2], "video_link": r[3], "blog_link": r[4]})

        return JsonResponse(lis, safe=False)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
def getMedia(req):
    try:

        media = TblMediaService.objects.all()
        serializer = MediaSerializer(media, many=True)
        return Response(serializer.data)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def addLoanDetails(req):
    try:
        # try:
        #     token = req.headers["authToken"]
        #     # print(token)
        #     response = verifyUser(token)
        #     if response["message"] is True:
        #         user_id = response['id']
        #         print(user_id, 'hola')
        #     else:
        #         return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        # except Exception as e:
        #     try:
        #         token = req.query_params['authToken']
        #         response = verifyUser(token)
        #         if response["message"] is True:
        #             user_id = response['id']
        #             print(user_id, 'hola')
        #         else:
        #             return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        #     except Exception as e:
        #         print(e.args)
        #         return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            params = json.loads(req.body)
            name = params["name"]
            email = params["email"]
            phone = params["phone"]
            type_loan = params["loan_type"]
            if len(phone) != 10:
                return Response({"message": "Wrong Number Format"},
                                status=status.HTTP_400_BAD_REQUEST)
            try:
                business_name = "'"+params["business_name"]+"'"
            except:
                business_name = "null"
        except:
            return Response({"message": "Necessary fields not found"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        now = getCurrentTimestamp()

        insert_q = "INSERT INTO `stocktick-qa`.tbl_loan_details (created_by,created_at,status,name,type_loan,email,phone,business_name) VALUES ('%s','%s','ACTIVE','%s','%s','%s','%s',%s)" % (
            -1, now, name, type_loan, email, phone, business_name)

        print(insert_q)
        cur.execute(insert_q)
        conn.commit()
        cur.close()
        # conn.close()

        return JsonResponse({"message": "SuccessFully added loan details"})

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def addContactUsDetails(req):
    try:
        try:
            params = json.loads(req.body)
            name = params["name"]
            email = params["email"]
            phone = params["phone"]
            query = params["query"]
            if len(phone) != 10:
                return Response({"message": "Wrong Number Format"},
                                status=status.HTTP_400_BAD_REQUEST)
        except:
            return Response({"message": "Necessary fields not found"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        now = getCurrentTimestamp()

        insert_q = "INSERT INTO `stocktick-qa`.tbl_contact_us (created_date,name,email,phone,query) VALUES ('%s','%s','%s','%s','%s')" % (
            now, name, email, phone, query)

        print(insert_q)
        cur.execute(insert_q)
        conn.commit()
        cur.close()
        # conn.close()

        return JsonResponse({"message": "SuccessFully registered your query."})

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def addInsuranceDetails(req):
    try:
        # try:
        #     token = req.headers["authToken"]
        #     # print(token)
        #     response = verifyUser(token)
        #     if response["message"] is True:
        #         user_id = response['id']
        #         print(user_id, 'hola')
        #     else:
        #         return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        # except Exception as e:
        #     try:
        #         token = req.query_params['authToken']
        #         response = verifyUser(token)
        #         if response["message"] is True:
        #             user_id = response['id']
        #             print(user_id, 'hola')
        #         else:
        #             return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        #     except Exception as e:
        #         print(e.args)
        #         return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            params = json.loads(req.body)
            ins_type = params["type"]
            if ins_type == "motor":
                manufac_date = params['manufac_date']
                manufacturer = params['manufacturer']
                claim_ins = params['current_policy_claim']
                name = params['name']
                email = params['email']
                mobile = params['mobile']
                vehicle_no = params['vehicle_number']
                model = params['model']
                fuel_type = params['fuel_type']
                variant = params['variant']
                current_insurer = params['current_insurer']
                puc = params['puc']
                puc_expiry = params['puc_expiry']
                month_12_owner = params['month_12_ownership']
                policy_expiry = params['policy_expiry']

                conn = connection
                cur = conn.cursor()

                now = getCurrentTimestamp()

                insert_q = "INSERT INTO `stocktick-qa`.tbl_insurance_car (`created_by`,`created_at`,`manufacer_date`, `manufacturer`, `current_policy_claim`, `name`, `email_id`, `mobile`, `vehicle_number`, `model`, `fuel_type`, `variant`, `current_insurer`, `PUC`, `PUC_expiry`, `month_12_ownership`, `policy_expiry_date`) VALUES ('%s', '%s', '%s', '%s', '%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')" % (
                    -1, now, manufac_date, manufacturer, claim_ins, name, email, mobile, vehicle_no, model, fuel_type, variant, current_insurer, puc, puc_expiry, month_12_owner, policy_expiry
                )

                cur.execute(insert_q)
                print(insert_q)
                conn.commit()
                cur.close()
                conn.close()

            elif ins_type == "life":
                name = params['name']
                location = params['location']
                email = params['email']
                mobile = params['mobile']
                dob = params['dob']
                cover_life = params['cover_life']
                cover_upto = params['cover_upto']
                amount = params['amount']

                conn = connection
                cur = conn.cursor()

                now = getCurrentTimestamp()

                insert_q = "INSERT INTO `stocktick-qa`.`tbl_insurance_life` (`created_by`, `created_at`, `name`, `location`, `email`, `mobile`, `dob`, `cover_life`, `cover_upto`, `amount`) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')" % (
                    -1, now, name, location, email, mobile, dob, cover_life, cover_upto, amount)

                cur.execute(insert_q)
                print(insert_q)
                conn.commit()

                cur.close()
                conn.close()

            elif ins_type == "health":
                name = params['name']
                location = params['location']
                email = params['email']
                mobile = params['mobile']
                age = params['age']
                insurance_for = params['insurance_for']
                insurer_name = params['insurer_name']
                insurer_age = params['insurer_age']
                medical = params['medical']

                conn = connection
                cur = conn.cursor()

                now = getCurrentTimestamp()

                insert_q = "INSERT INTO `stocktick-qa`.`tbl_insurance_health` (`created_at`, `created_by`, `name`, `location`, `email`, `mobile`, `age`, `insurance_for`, `insurer_name`, `insurer_age`, `medical_condition`) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')" % (
                    now, -1, name, location, email, mobile, age, insurance_for, insurer_name, insurer_age, medical
                )
                cur.execute(insert_q)
                print(insert_q)

                cur.close()
            else:
                return Response({"message": "Wrong insurance type or input values are incorrect"}, status=status.HTTP_400_BAD_REQUEST)

            return JsonResponse({"message": "Successfully Added Insurance"})
        except Exception as e:
            print(e.args[0])
            return Response({"message": "Input Fields missing "}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
def getWebinar(req):
    try:
        # try:
        #     token = req.headers["authToken"]
        #     # print(token)
        #     response = verifyUser(token)
        #     if response["message"] is True:
        #         user_id = response['id']
        #         print(user_id, 'hola')
        #     else:
        #         return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        # except Exception as e:
        #     print(e.args)
        #     return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        qq = "SELECT title,short_desc,long_desc,image_url,hosted_by,other_host_name,webinar_redirect_url,id,date FROM `stocktick-qa`.tbl_webinars WHERE status='ACTIVE'"

        cur.execute(qq)
        res = cur.fetchall()

        lis = []
        for r in res:
            lis.append(
                {"title": r[0], "short_desc": r[1], "long_desc": r[2], "image_url": r[3], "hosted_by": r[4], "other_host_name": r[5], "webinar_redirect_url": r[6], "id": r[7], "date": r[8]})

        return JsonResponse(lis, safe=False)
    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def subscribeWebinar(req):
    try:
        # try:
        #     token = req.headers["authToken"]
        #     # print(token)
        #     response = verifyUser(token)
        #     if response["message"] is True:
        #         user_id = response['id']
        #         print(user_id, 'hola')
        #     else:
        #         return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        # except Exception as e:
        #     try:
        #         token = req.query_params['authToken']
        #         response = verifyUser(token)
        #         if response["message"] is True:
        #             user_id = response['id']
        #             print(user_id, 'hola')
        #         else:
        #             return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        #     except Exception as e:
        #         print(e.args)
        #         return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            params = json.loads(req.body)
            webinar_id = params["webinar_id"]
            name = params["name"]
            email = params["email"]
            phone = params["phone"]
            city = params["city"]
        except:
            return Response({"message": "Necessary fields not found"}, status=status.HTTP_404_NOT_FOUND)

        conn = connection
        cur = conn.cursor()

        q = "SELECT hosted_by FROM`stocktick-qa`.tbl_webinars WHERE id = %s " % webinar_id

        cur.execute(q)
        res = cur.fetchone()
        print(q)
        if not res:
            return Response({"message": "Wrong webinar id"},
                            status=status.HTTP_400_BAD_REQUEST)
        # print(res)
        if res[0] == 'self':
            webinar_status = 'clicked'
        else:
            webinar_status = 'registered'
            # sendmail(to,body)
        now = getCurrentTimestamp()

        # check if already subscribed
        # q = "SELECT * FROM `stocktick-qa`.tbl_webinar_subscription WHERE webinar_id = %s AND user_id = %s" % (
        #     webinar_id, user_id)
        # cur.execute(q)
        # r = cur.fetchall()
        # if r:
        #     body = "You have succesfully registered for the webinar."
        #     # try:
        #     #     sendemail(params["email"], 'Webinar Registration', body)
        #     # except Exception as e:
        #     #     print("Error yaar")
        #     #     print(e)
        #     return Response({"message": "Already Registered"},
        #                     status=status.HTTP_400_BAD_REQUEST)

        insert_q = "INSERT INTO `stocktick-qa`.tbl_webinar_subscription (created_by,created_at,status,webinar_id,user_id,webinar_status,name,email,phone,city) VALUES ('%s','%s','ACTIVE',%s,%s,'%s','%s','%s','%s','%s')" % (
            7, now, webinar_id, 7, webinar_status, name, email, phone, city
        )

        print(insert_q)
        cur.execute(insert_q)
        conn.commit()
        cur.close()
        # conn.close()
        # if res[0] == 'self':
        #     body = "You have succesfully registered for the webinar."
        #     sendemail(params["email"], 'Webinar Registration', body)

        return JsonResponse({"message": "SuccessFully registered"})

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
