from rest_framework import serializers
from .models import TblMediaService, TblHeadlineService


class MediaSerializer(serializers.ModelSerializer):
    class Meta:
        model = TblMediaService
        # fields = ['id', 'title', 'author','email']
        fields="__all__"

class HeadlineSerializer(serializers.ModelSerializer):
    class Meta:
        model = TblHeadlineService
        # fields = ['id', 'title', 'author','email']
        fields="__all__"