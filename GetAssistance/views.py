from distutils.util import execute
import json
from os import remove
from urllib import response

from users.auth import verifyUser

# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
# from itsdangerous import Serializer
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings


from .serializers import GetAssistanceSerializer


class GetAssistanceAPIView(APIView):

    def post(self, request):
        data = request.data
        # token = request.headers["authToken"]
        try:
            name = data["name"]
            email = data["email"]
            phone = data["phone"]
            query = data["query"]
            # page_name = data["page_name"]
            # device = data["device"]
        except:
            return Response({"message": "All fields are mandatory to fill"}, status=status.HTTP_404_NOT_FOUND)
        # try:
        #     resp = verifyUser(token)
        #     if resp["message"]:
        #         userId = resp["id"]
        #     else:
        #         return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        # except Exception as e:
        #     print(e.args[0])
        #     return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        if data:
            # data["created_by"] = userId
            # data["modified_by"] = userId
            # data["user_id"] = userId
            data["Response_data"] = None

        serializer = GetAssistanceSerializer(data=request.data)
        print(serializer)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)
