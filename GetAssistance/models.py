from lib2to3.pgen2 import token
from math import fabs
from urllib import response
from django.db import models
from users.constant import ROW_STATUS, ACTIVE

# Create your models here.


class GetAssistance(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    name = models.TextField(max_length=50, blank=True, null=True)
    page_name = models.TextField(max_length=100, blank=True, null=True)
    device = models.TextField(max_length=50, blank=True, null=True)
    query = models.TextField(max_length=500, blank=True, null=True)
    user_id = models.IntegerField(blank=True, null=False)
    email = models.EmailField(max_length=100, blank=False, null=False)
    phone = models.Char<PERSON><PERSON>(max_length=12, blank=True, null=False)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    Response_data = models.TextField(max_length=100, blank=True, null=True)

    def __str__(self):
        return str(self.query)

    class Meta:
        managed = False
        db_table = 'tbl_get_assistance'
