# Generated by Django 4.0.3 on 2022-03-29 16:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='GetAssistance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.TextField(blank=True, max_length=50, null=True)),
                ('page_name', models.TextField(blank=True, max_length=50, null=True)),
                ('device', models.TextField(blank=True, max_length=50, null=True)),
                ('query', models.TextField(blank=True, max_length=500, null=True)),
                ('user_id', models.IntegerField(unique=True)),
                ('email', models.EmailField(max_length=100)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('Response_data', models.TextField(blank=True, default=None, max_length=100, null=True)),
            ],
            options={
                'db_table': 'tbl_get_assistance',
                'managed': False,
            },
        ),
    ]
