from django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
# from rest_framework.routers import DefaultRouterfrom django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
from rest_framework.routers import DefaultRouter


from .views import GetAssistanceAPIView


urlpatterns = [
    # path('article/', article_list), # Function based calls
    path('', GetAssistanceAPIView.as_view()),
   


]
