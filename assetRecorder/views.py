from distutils.util import execute
import json
from os import remove, stat
from tabnanny import check
# from tkinter import Exception
from urllib import response

from users.auth import verifyUser

# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
# from itsdangerous import Serializer
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings

from uuid import uuid4
import pytz
import datetime
from .models import *

from .utils import *
from users.constant import ROW_STATUS, ACTIVE
import pandas as pd
from .serializers import *
from UliPlot.XLSX import auto_adjust_xlsx_column_width


class personalAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None
        try:
            pers = personal.objects.filter(status=ACTIVE, userId=userId)
            pers = personalSerializer(pers, many=True)
            if not pers:
                return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)
            return Response(pers.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        print("check", userId)
        try:
            exists = personal.objects.get(userId=userId)
        except:

            exists = -1

        if (exists != -1):
            if data:
                for keyss, val in data.items():
                    setattr(exists, keyss, val)
            exists.status = ACTIVE
            exists.modified_by = userId
            exists.save()
            return Response({"message": "Data Updated"}, status=status.HTTP_201_CREATED)

        else:
            print("check2")
            if data:
                print("check3")
                data["created_by"] = userId
                data["modified_by"] = userId
                data["userId"] = userId

            serializer = personalSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class keyPeopleAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None
        try:
            key = keyPeople.objects.filter(status=ACTIVE, userId=userId)
            key = keyPeopleSerializer(key, many=True)
            if not key:
                return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)
            return Response(key.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        print("check", userId)
        try:
            exists = keyPeople.objects.get(userId=userId)
        except:

            exists = -1

        if (exists != -1):
            if data:
                for keyss, val in data.items():
                    setattr(exists, keyss, val)
            exists.status = ACTIVE
            exists.modified_by = userId
            exists.save()
            return Response({"message": "Data Updated"}, status=status.HTTP_201_CREATED)

        else:
            if data:
                data["created_by"] = userId
                data["modified_by"] = userId
                data["userId"] = userId

            serializer = keyPeopleSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class dataAPIView(APIView):

    def get(self, request):
        dataa = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            asset = request.query_params["asset"]
        except Exception as e:
            print(e)
            asset = ""

        try:
            if (asset == ""):
                dat = dataCommon.objects.filter(status=ACTIVE, userId=userId)
                dat = dataCommonSerializer(dat, many=True)
                dat = dat.data

            else:
                dat = dataCommon.objects.filter(
                    status=ACTIVE, userId=userId, type=asset)
                dat = dataCommonSerializer(dat, many=True)
                dat = dat.data
            for val in dat:
                dictt = fetch_json(val['id'])
                try:
                    for key, vals in dictt['data'].items():
                        val[key] = vals
                except Exception as e:
                    print("data nahi he", e)

            if not dat:
                return Response({"msg": "Data not found1"}, status=status.HTTP_404_NOT_FOUND)
            return Response(dat, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"msg": "Data not found2"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        dataa = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            asset = request.query_params["asset"]
            if asset == "":
                return Response({"message": "asset cannot be empty!"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"message": "asset param is not passed"}, status=status.HTTP_404_NOT_FOUND)

        try:
            editId = request.query_params["editId"]
        except Exception as e:
            print(e)
            editId = 0

        print("check", userId)
        try:
            exists = dataCommon.objects.get(
                userId=userId, type=asset, status=ACTIVE, id=editId)
        except:

            exists = -1

        common = ['id', 'created_by', 'created_at', 'modified_by', 'modified_at', 'userId', 'status',
                  'nominee1', 'relationship1', 'allocation1', 'nominee2', 'relationship2', 'allocation2']
        if (exists != -1):

            if dataa['nominee1']:
                exists.nominee1 = dataa['nominee1']
            if dataa['relationship1']:
                exists.relationship1 = dataa['relationship1']
            if dataa['allocation1']:
                exists.allocation1 = dataa['allocation1']
            if dataa['nominee2']:
                exists.nominee2 = dataa['nominee2']
            if dataa['relationship2']:
                exists.relationship2 = dataa['relationship2']
            if dataa['allocation2']:
                exists.allocation2 = dataa['allocation2']
            exists.status = ACTIVE
            exists.modified_by = userId
            exists.save()

            jsonex = dataJson.objects.get(status=ACTIVE, typeId=exists.id)
            jsonexdata = jsonex.data
            for key, val in dataa.items():
                if key not in common:
                    jsonexdata[key] = val

            jsonex.data = jsonexdata
            jsonex.save()

            return Response({"message": "data updated", }, status=status.HTTP_201_CREATED)

        else:
            print("new")
            if dataa:
                print(f"inside if dataa {dataa}")
                dataa["created_by"] = userId
                dataa["modified_by"] = userId
                dataa["userId"] = userId
                dataa['type'] = asset

            jsondic = {}

            serializer = dataCommonSerializer(data=dataa)
            if serializer.is_valid():
                serializer.save()
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            try:
                exist = dataCommon.objects.latest('id')
            except Exception as e:
                return Response(e, status=status.HTTP_400_BAD_REQUEST)

            jsondic['typeId'] = exist.id
            jsondic["created_by"] = userId
            jsondic["modified_by"] = userId
            jsondic['data'] = (check_each(dataa, asset))
            serializer2 = dataJsonSerializer(data=jsondic)
            if serializer2.is_valid():
                serializer2.save()
                return Response({"ser1": serializer.data, "ser2": serializer2.data}, status=status.HTTP_201_CREATED)
            else:
                return Response(serializer2.errors, status=status.HTTP_400_BAD_REQUEST)


class dataDeleteAPIView(APIView):
    def post(self, request):
        dataa = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            stat = request.query_params["status"]
        except:
            stat = 'DELETED'

        try:
            deleteId = request.query_params["deleteId"]
            if deleteId == "":
                return Response({"message": "deleteId cannot be empty!"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"message": "deleteId param is not passed"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            exists = dataCommon.objects.get(userId=userId, id=deleteId)

            if stat == 'DELETED' or stat == 'INACTIVE' or stat == 'ACTIVE':
                dataJson.objects.filter(typeId=exists.id).update(status=stat)
                dataCommon.objects.filter(
                    userId=userId, id=deleteId).update(status=stat)

                return Response({"message": "data has been "+stat, }, status=status.HTTP_201_CREATED)
            else:
                dataJson.objects.filter(typeId=exists.id).delete()
                dataCommon.objects.filter(userId=userId, id=deleteId).delete()

                return Response({"message": "data has been permanently deleted", }, status=status.HTTP_201_CREATED)

        except Exception as e:
            print(e)
            return Response({"message": "data does not exist "}, status=status.HTTP_404_NOT_FOUND)


class CSVAPIView(APIView):

    def get(self, request):
        dataa = request.data
        lastUsedNumber = {}
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        filen = os.path.join("assetRecorder", f"{userId}.xlsx")
        writer = pd.ExcelWriter(filen)

        to_remove = ["id", "created_by", "created_at",
                     "modified_by", "modified_at", "userId"]

        pers = personal.objects.filter(status=ACTIVE, userId=userId)
        pers = personalSerializer(pers, many=True)

        if pers and pers.data and len(pers.data) > 0:
            pers = pers.data[0]
            for i in to_remove:
                if i in pers.keys():
                    del pers[i]
            df = pd.DataFrame(pers, index=[0])
            df.rename(columns=keyNameMap, inplace=True)
            df = df.T
            ty = "Personal Info"
            # df.to_csv(f"./assetRecorder/{userId}-{ty}.xlsx")
            df.to_excel(writer, ty, header=False)
            auto_adjust_xlsx_column_width(df, writer, sheet_name=ty, margin=0)

        key = keyPeople.objects.filter(status=ACTIVE, userId=userId)
        key = keyPeopleSerializer(key, many=True)
        
        if key and key.data and len(key.data)>0:
            key = key.data[0]
            for i in to_remove:
                if i in key.keys():
                    del key[i]
            df = pd.DataFrame(key, index=[0])
            df.rename(columns=keyNameMap, inplace=True)
            df = df.T
            ty = "Key Persons"
            # df.to_csv(f"./assetRecorder/{userId}-{ty}.xlsx")
            df.to_excel(writer, ty, header=False)
            auto_adjust_xlsx_column_width(df, writer, sheet_name=ty, margin=0)

        try:
            dat = dataCommon.objects.filter(status=ACTIVE, userId=userId)
            dat = dataCommonSerializer(dat, many=True)
            dat = dat.data

            for val in dat:
                try:
                    dictt = fetch_json(val['id'])
                    for key, vals in dictt['data'].items():
                        val[key] = vals
                except Exception as e:
                    print("data nahi he", e)

            if not dat:
                return Response({"msg": "Data not found1"}, status=status.HTTP_404_NOT_FOUND)

            fullData = dict()

            for dic in dat:
                # print("inside dat")
                for i in to_remove:
                    if i in dic.keys():
                        del dic[i]
                df = pd.DataFrame(dic, index=[0])
                # print("Toop lebel columns")
                # print(df.columns)
                df.rename(columns=keyNameMap, inplace=True)
                # print("After toop level")
                # print(df.columns)

                ty = keyNameMap.get(dic["type"], dic["type"])
                print(ty)
                # print(ty)
                if ty == "Fixed Deposit":
                    df = df[["Bank Name", "Branch Address", "Deposit Holder Name", "Amount Invested", "Investment Date", "Investment Duration", "Rate of Interest", "Maturity Date",
                             "Maturity Amount", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Health Insurance":
                    df = df[["Plan Name", "Policy Number", "Policy Holder Name", "Family Member Covered", "Assured Sum", "Policy Start Date", "Renewal Date", "Premium Amount",
                             "TPA Account Number", "Company Number", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Mutual Funds":
                    df = df[["Scheme Name", "Purchase Amount", "Purchase Nav", "Current Nav", "Current Amount", "Div Amount", "Short Gain", "Long Gain",
                             "Absolute Return", "Compound Annual Growth Rate", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "National Pension Scheme":
                    df = df[["Permanent Account Number", "Current Balance", "Account Type", "Bank Details",
                             "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Motor Insurance":
                    df = df[["Plan Name", "Policy Number", "Insured Declared Value", "Type Start Cover", "Policy Holder Name", "Policy Start Date", "Premium Amount",
                             "Renewal Date", "Company Helpdesk Number", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "loan":
                    df = df[["Bank Name", "Loan Account Number", "Loan Amount", "Asset Name", "Asset Type", "Loan Start Date",
                             "Loan Tenure", "EMI Amount", "Rate of Interest", "ECS Bank", "Insurance Protected Loan", "Current Outstanding"]]
                elif ty == "Portfolio Management Service":
                    df = df[["Asset Management Company Name", "Portfolio Management Service Name", "Invested Value", "Invested Date", "Current Amount", "Account Number",
                             "Absolute Return", "Compound Annual Growth Rate", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Alternative Investment Fund":
                    df = df[["Asset Management Company Name", "Alternative Investment Funds Name", "Invested Value", "Invested Date", "Current Amount", "Account Number",
                             "Absolute Return", "Compound Annual Growth Rate", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Indian Equity" or ty == "Bonds" or ty == "OFF Market":
                    df = df[["Broker Name", "Account Number", "Instrument Name", "Quantity", "Purchase Price", "Purchase Value", "Market Price",
                             "Market Value", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Employee Provident Fund":
                    df = df[["Employee Provident Fund Number", "Balance", "Nominee I", "Relationship I",
                             "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Public Provident Fund":
                    df = df[["Bank Name", "Branch Address", "PPF Account Number", "Balance", "Nominee I",
                             "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "US Equity":
                    df = df[["Broker Name", "Account Number", "Instrument Name", "Quantity", "Purchase Price", "Purchase Value", "Market Price",
                             "Market Value", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Real Estate":
                    df = df[["Property Name", "Invested Value", "Invested Date", "Current Value", "Property Type", "Property Size", "Location Pincode",
                             "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Life Insurance":
                    df = df[["Plan Name", "Policy Number", "Policy Holder Name", "Assured Sum", "Policy Start Date", "Policy Maturity Date", "Policy Term", "Premium Payment Term(Years)",
                             "Premium Amount", "Premium Payment Frequency", "Renewal Date", "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Bank Account":
                    df = df[["Bank Name", "Branch Address", "Account Holder", "Last Digit of Account", "Account Type",
                             "Nominee I", "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]
                elif ty == "Locker":
                    df = df[["Bank Name", "Bank Address", "Locker Number", "Locker Holder Name", "Nominee I",
                             "Relationship I", "Allocation I", "Nominee II", "Relationship II", "Allocation II"]]

                if ty not in fullData:
                    fullData[ty] = df
                else:
                    print("Reported same file name......", ty)
                    print(fullData[ty].columns)

                    df2 = fullData[ty]
                    df_all_rows = pd.concat([df2, df])
                    fullData[ty] = df_all_rows

                # df = df.T
                # sheetName = ty
                # if (lastUsedNumber.get(sheetName, -1) == -1):
                #     lastUsedNumber[ty] = 1
                # sheetName += " " + str(lastUsedNumber[ty])
                # lastUsedNumber[ty] = lastUsedNumber[ty]+1
                # # df.to_csv(f"./assetRecorder/{userId}-{ty}.xlsx")
                # df.to_excel(writer, sheetName, header=False)
                # auto_adjust_xlsx_column_width(
                #     df, writer, sheet_name=sheetName, margin=0)
            flg = 1
            print("Mapping complete.....")
            # try:
            for xx in fullData:
                print(fullData)
                df = fullData[xx]
                df = df.apply(lambda y: y.astype(str).str.upper())
                #df = df.applymap(lambda s: s.lower() if type(s) == str else s)
                df = df.T

                try:
                    df.to_excel(writer, xx, header=False)
                    auto_adjust_xlsx_column_width(
                        df, writer, sheet_name=xx, margin=10)
                except Exception as e:
                    print(e)

            try:
                writer.save()
                # writer.close()
            except Exception as e:
                print(e)
                flg = -1

            k = False
            k = uploadFile(userId, writer)
            print(k)
            if flg == 1 and k:
                return Response({"message": "Report Generated and Uploaded", "url": k}, status=status.HTTP_201_CREATED)
            elif flg == 1 and not k:
                return Response({"message": "Report Generated but uploading file failed"}, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({"message": "Report Not Generated"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            print(e)
            return Response({"msg": "Data not found2"}, status=status.HTTP_404_NOT_FOUND)


class assetsAPISummary(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            print(resp)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None

        assetsValue = 0
        liabilitiesValue = 0

        try:
            print(userId)
            assetsData = dataJson.objects.filter(
                status=ACTIVE, created_by=userId)

            # print(assetsData)
            # assetsData=assetsData

            # print(assetsData)
            for asset in assetsData:
                asset = asset.data

                if (asset.get('amount_invested', 0)):
                    # print("Here1")
                    assetsValue += float(asset['amount_invested'])

                if (asset.get('current_amount', 0)):
                    # print("Here2")
                    assetsValue += float(asset['current_amount'])

                if (asset.get('market_value', 0)):
                    # print("Here3")
                    assetsValue += float(asset['market_value'])

                if (asset.get('current_value', 0)):
                    # print("Here4")
                    assetsValue += float(asset['current_value'])

                if (asset.get('sum_assured', 0)):
                    # print("Here5")
                    assetsValue += float(asset['sum_assured'])

                if (asset.get('insured_declared_value', 0)):
                    # print("Here5")
                    assetsValue += float(asset['insured_declared_value'])

                if (asset.get('loan_amount', 0)):
                    # print("Here6")
                    liabilitiesValue += float(asset['loan_amount'])

            return Response({"assets": round(assetsValue), "liabilities": round(liabilitiesValue)}, status=status.HTTP_200_OK)

        except Exception as e:
            print(e)
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

        # try:
        #     pers = personal.objects.filter(status = ACTIVE,userId = userId)
        #     pers = personalSerializer(pers,many=True)
        #     if not pers:
        #         return Response({"msg":"Data not found"}, status=status.HTTP_404_NOT_FOUND)
        #     return Response(pers.data, status=status.HTTP_201_CREATED)
        # except Exception as e:
        #     return Response({"msg":"Data not found"}, status=status.HTTP_404_NOT_FOUND)
