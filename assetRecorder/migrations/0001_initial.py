# Generated by Django 4.0.4 on 2022-06-09 09:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='data',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('userId', models.IntegerField(unique=True)),
                ('fd', models.JSONField(blank=True, null=True)),
                ('bank_acoount', models.JSONField(blank=True, null=True)),
                ('mf', models.JSONField(blank=True, null=True)),
                ('locker', models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('equity_crypto', models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('bonds_offmarket', models.JSONField(blank=True, null=True)),
                ('aif_pms', models.J<PERSON>NField(blank=True, null=True)),
                ('epf', models.JSONField(blank=True, null=True)),
                ('ppf', models.JSONField(blank=True, null=True)),
                ('real_estate', models.JSONField(blank=True, null=True)),
                ('nps_tier1', models.JSONField(blank=True, null=True)),
                ('others', models.JSONField(blank=True, null=True)),
                ('life_insurance', models.JSONField(blank=True, null=True)),
                ('health_insurance', models.JSONField(blank=True, null=True)),
                ('motor_insurance', models.JSONField(blank=True, null=True)),
                ('loan', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_asset_data',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='keyPeople',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('userId', models.IntegerField(unique=True)),
                ('family_doctor', models.TextField(blank=True, null=True)),
                ('CA', models.TextField(blank=True, null=True)),
                ('advocate', models.TextField(blank=True, null=True)),
                ('advisor', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_asset_key_people',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='personal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('userId', models.IntegerField(unique=True)),
                ('name', models.TextField(max_length=50)),
                ('address', models.TextField(blank=True, max_length=300, null=True)),
                ('mobile', models.TextField(blank=True, max_length=15, null=True)),
                ('email_id', models.EmailField(blank=True, max_length=254, null=True)),
                ('dob', models.DateField(blank=True, null=True)),
                ('blood_group', models.TextField(blank=True, null=True)),
                ('emergency_name', models.TextField(max_length=50)),
                ('emergency_address', models.TextField(blank=True, max_length=300, null=True)),
                ('emergency_contact', models.TextField(blank=True, max_length=15, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_asset_personal',
                'managed': False,
            },
        ),
    ]
