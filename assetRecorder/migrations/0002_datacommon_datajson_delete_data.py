# Generated by Django 4.0.4 on 2022-06-13 09:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assetRecorder', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='dataCommon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('userId', models.IntegerField(unique=True)),
                ('nominee1', models.TextField(blank=True, null=True)),
                ('relationship1', models.TextField(blank=True, null=True)),
                ('allocation1', models.TextField(blank=True, null=True)),
                ('nominee2', models.TextField(blank=True, null=True)),
                ('relationship2', models.TextField(blank=True, null=True)),
                ('allocation2', models.TextField(blank=True, null=True)),
                ('type', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_asset_data_common',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='dataJson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('typeId', models.IntegerField(unique=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_asset_data_json',
                'managed': False,
            },
        ),
        migrations.DeleteModel(
            name='data',
        ),
    ]
