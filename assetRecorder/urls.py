from django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
# from rest_framework.routers import DefaultRouterfrom django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
from rest_framework.routers import DefaultRouter


from .views import *


urlpatterns = [
    # path('article/', article_list), # Function based calls
    path('pers/', personalAPIView.as_view()),
    path('keyp/', keyPeopleAPIView.as_view()),
    path('data/', dataAPIView.as_view()),
    path('data-delete/', dataDeleteAPIView.as_view()),
    path('data-summary/', assetsAPISummary.as_view()),
    path('csv/', CSVAPIView.as_view()),
    # path('result/', RangeResultAPIView.as_view()),


]
