from rest_framework import serializers

from .models import *


class personalSerializer(serializers.ModelSerializer):
    class Meta:
        model = personal
        fields = "__all__"

class keyPeopleSerializer(serializers.ModelSerializer):
    class Meta:
        model = keyPeople
        fields = "__all__"

class dataCommonSerializer(serializers.ModelSerializer):
    class Meta:
        model = dataCommon
        fields = "__all__"

class dataJsonSerializer(serializers.ModelSerializer):
    class Meta:
        model = dataJson
        fields = "__all__"
