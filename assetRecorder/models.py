from lib2to3.pgen2 import token
from math import fabs
from pyexpat import model
from django.db import models
from django.forms import J<PERSON><PERSON><PERSON>
from users.constant import ROW_STATUS, ACTIVE, ROW_LOCK, LOCKED

# Create your models here.
class personal(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    userId = models.IntegerField(blank=False, null=False,unique=True)
    name = models.TextField(max_length=50, blank=False, null=False)
    address = models.TextField(max_length=300, blank=True, null=True)
    mobile =models.TextField(max_length=15, blank=True, null=True)
    email_id = models.EmailField(blank=True, null=True)
    dob = models.DateField(blank=True, null=True)
    blood_group = models.TextField(blank=True, null=True)
    emergency_name = models.TextField(max_length=50, blank=False, null=False)
    emergency_address = models.TextField(max_length=300, blank=True, null=True)
    emergency_contact = models.TextField(max_length=15, blank=True, null=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)

    def __str__(self):
        return str(self.name) 

    class Meta:
        managed = False
        db_table = 'tbl_asset_personal'


class keyPeople(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    userId = models.IntegerField(blank=False, null=False,unique=True)
    family_doctor = models.TextField(blank=True, null=True)
    family_doctor_c1 = models.TextField(blank=True, null=True)
    family_doctor_c2 = models.TextField(blank=True, null=True)
    CA = models.TextField(blank=True, null=True)
    CA_c1 = models.TextField(blank=True, null=True)
    CA_c2 = models.TextField(blank=True, null=True)
    advocate = models.TextField(blank=True, null=True)
    advocate_c1 = models.TextField(blank=True, null=True)
    advocate_c2 = models.TextField(blank=True, null=True)
    advisor = models.TextField(blank=True, null=True)
    advisor_c1 = models.TextField(blank=True, null=True)
    advisor_c2 = models.TextField(blank=True, null=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    def __str__(self):
        return str(self.userId)

    class Meta:
        managed = False
        db_table = 'tbl_asset_key_people'



class dataCommon(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    userId = models.IntegerField(blank=False, null=False)
    nominee1 = models.TextField(blank=True, null=True)
    relationship1 = models.TextField(blank=True, null=True)
    allocation1 = models.TextField(blank=True, null=True)
    nominee2 = models.TextField(blank=True, null=True)
    relationship2 = models.TextField(blank=True, null=True)
    allocation2 = models.TextField(blank=True, null=True)
    type = models.TextField(blank=True,null=True)
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    def __str__(self):
        return str(self.userId)

    class Meta:
        managed = False
        db_table = 'tbl_asset_data_common'

class dataJson(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    typeId = models.IntegerField(blank=False, null=False,unique=True)
    data = models.JSONField()
    status = models.CharField(blank=False, null=False, choices=ROW_STATUS, default=ACTIVE, max_length=10)
    def __str__(self):
        return str(self.typeId) + str(self.data)

    class Meta:
        managed = False
        db_table = 'tbl_asset_data_json'

