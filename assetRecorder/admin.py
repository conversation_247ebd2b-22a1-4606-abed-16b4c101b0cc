from django.contrib import admin
from .models import *

@admin.register(personal)
class PostAdmin(admin.ModelAdmin):
    list_display = ('userId', 'name','status')


@admin.register(keyPeople)
class PostAdmin(admin.ModelAdmin):
    list_display = ('userId','status')

@admin.register(dataCommon)
class PostAdmin(admin.ModelAdmin):
    list_display = ('userId','status')

@admin.register(dataJson)
class PostAdmin(admin.ModelAdmin):
    list_display = ('typeId','status')


