import json
# Create your views here.

import random
from uuid import uuid4
from .models import *
from .serializers import *
from users.models import *
from users.serializer import *
import os
import boto3
from django.conf import settings

from users.constant import ACTIVE

""" Mapping of the keys and their names in normal english"""
keyNameMap = {}
keyNameMap['bank_name'] = "Bank Name"
keyNameMap["branch_address"] = "Branch Address"
keyNameMap["deposit_name"] = "Deposit Holder Name"
keyNameMap["amount_invested"] = "Amount Invested"
keyNameMap["invested_value"]="Invested Value"
keyNameMap["investment_date"] = "Investment Date"
keyNameMap["investment_duration"] = "Investment Duration"
keyNameMap["rate_interest"] = "Rate of Interest"
keyNameMap["maturity_date"] = "Maturity Date"
keyNameMap["maturity_amount"] = "Maturity Amount"
keyNameMap["scheme_name"] = "Scheme Name"
keyNameMap["purchase_amount"] = "Purchase Amount"
keyNameMap["puchase_nav"] = "Purchase Nav"
keyNameMap["current_nav"] = "Current Nav"
keyNameMap["current_amount"] = "Current Amount"
keyNameMap["div_amount"] = "Div Amount"
keyNameMap["gain_short"] = "Short Gain"
keyNameMap["gain_long"] = "Long Gain"
keyNameMap["return_abs"] = "Absolute Return"
keyNameMap["return_cagr"] = "Compound Annual Growth Rate"
keyNameMap["account_holder"] = "Account Holder"
keyNameMap["last_digit_account"] = "Last Digit of Account"
keyNameMap["account_type"] = "Account Type"
keyNameMap["locker_number"] = "Locker Number"
keyNameMap["locker_name"] = "Locker Holder Name"
keyNameMap["broker_name"] = "Broker Name"
keyNameMap["account_number"] = "Account Number"
keyNameMap["instrument_name"] = "Instrument Name"
keyNameMap["qty"] = "Quantity"
keyNameMap["purchase_price"] = "Purchase Price"
keyNameMap["purchase_value"] = "Purchase Value"
keyNameMap["market_price"] = "Market Price"
keyNameMap["market_value"] = "Market Value"
keyNameMap["amc_name"] = "Asset Management Company Name"
keyNameMap["aif_name"] = "Alternative Investment Funds Name"
keyNameMap["interested_value"] = "Interest Value"
keyNameMap["invested_date"] = "Invested Date"
keyNameMap["pms_name"] = "Portfolio Management Service Name"
keyNameMap["epf_number"] = "Employee Provident Fund Number"
keyNameMap["balance"] = "Balance"
keyNameMap["ppf_account_number"] = "PPF Account Number"
keyNameMap["property_name"] = "Property Name"
keyNameMap["current_value"] = "Current Value"
keyNameMap["property_type"] = "Property Type"
keyNameMap["property_size"] = "Property Size"
keyNameMap["location_pincode"] = "Location Pincode"
keyNameMap["pran"] = "Permanent Account Number"
keyNameMap["current_balance"] = "Current Balance"
keyNameMap["nominee_details"] = "Nominee Details"
keyNameMap["bank_details"] = "Bank Details"
keyNameMap["purchase_date"] = "Purchase Date"
keyNameMap["units"] = "Units"
keyNameMap["plan_name"] = "Plan Name"
keyNameMap["policy_number"] = "Policy Number"
keyNameMap["policy_holder_name"] = "Policy Holder Name"
keyNameMap["sum_assured"] = "Assured Sum"
keyNameMap["policy_start_date"] = "Policy Start Date"
keyNameMap["policy_maturity_date"] = "Policy Maturity Date"
keyNameMap["policy_term"] = "Policy Term"
keyNameMap["premium_payment_term"] = "Premium Payment Term(Years)"
keyNameMap["premium_amount"] = "Premium Amount"
keyNameMap["premium_payment_frequency"] = "Premium Payment Frequency"
keyNameMap["renewal_date"] = "Renewal Date"
keyNameMap["family_member_covered"] = "Family Member Covered"
keyNameMap["tpa_account_number"] = "TPA Account Number"
keyNameMap["company_number"] = "Company Number"
keyNameMap["insured_declared_value"] = "Insured Declared Value"
keyNameMap["type_start_cover"] = "Type Start Cover"
keyNameMap["company_helpdesk_number"] = "Company Helpdesk Number"
keyNameMap["loan_account_number"] = "Loan Account Number"
keyNameMap["loan_amount"] = "Loan Amount"
keyNameMap["asset_name"] = "Asset Name"
keyNameMap["asset_type"] = "Asset Type"
keyNameMap["loan_start_date"] = "Loan Start Date"
keyNameMap["loan_tenure"] = "Loan Tenure"
keyNameMap["emi_amount"] = "EMI Amount"
keyNameMap["roi"] = "Rate of Interest"
keyNameMap["ecs_bank"] = "ECS Bank"
keyNameMap["insurance_protect_loan"] = "Insurance Protected Loan"
keyNameMap["current_outstanding"] = "Current Outstanding"
keyNameMap["fd"] = "Fixed Deposit"
keyNameMap["bank_account"] = "Bank Account"
keyNameMap["mf"] = "Mutual Funds"
keyNameMap["locker"] = "Locker"
keyNameMap["indian_equity"] = "Indian Equity"
keyNameMap["us_equity"] = "US Equity"
keyNameMap["crypto"] = "Crypto"
keyNameMap["bonds"] = "Bonds"
keyNameMap["off_market"] = "OFF Market"
keyNameMap["aif"] = "Alternative Investment Fund"
keyNameMap["pms"] = "Portfolio Management Service"
keyNameMap["epf"] = "Employee Provident Fund"
keyNameMap["ppf"] = "Public Provident Fund"
keyNameMap["real_estate"] = "Real Estate"
keyNameMap["nps_tier1"] = "National Pension Scheme"
keyNameMap["others"] = "Others"
keyNameMap["life_insurance"] = "Life Insurance"
keyNameMap["health_insurance"] = "Health Insurance"
keyNameMap["motor_insurance"] = "Motor Insurance"
keyNameMap["relationship1"] = "Relationship I"
keyNameMap["relationship2"] = "Relationship II"
keyNameMap["nominee1"] = "Nominee I"
keyNameMap["nominee2"] = "Nominee II"
keyNameMap["allocation2"] = "Allocation II"
keyNameMap["allocation1"] = "Allocation I"
keyNameMap["family_doctor"] = "Family Doctor"
keyNameMap["family_doctor_c1"] = "Family Doctor Contact Number"
keyNameMap["family_doctor_c2"] = "Family Doctor Alternate Contact Number"
keyNameMap["CA"] = "Chartered Accountant"
keyNameMap["CA_c1"] = "Chartered Accountant Contact Number"
keyNameMap["CA_c2"] = "Chartered Accountant Alternate Contact Number"
keyNameMap["advocate"] = "Advocate"
keyNameMap["advocate_c1"] = "Advocate Contact Number"
keyNameMap["advocate_c2"] = "Advocate Alternate Contact Number"
keyNameMap["advisor"] = "Advisor"
keyNameMap["advisor_c1"] = "Financial Advisor Contact Number"
keyNameMap["advisor_c2"] = "Financial Advisor Alternate Contact Number"
keyNameMap["email_id"] = "Email Id"
keyNameMap["dob"] = "Date of Birth"
keyNameMap["blood_group"] = "Blood Group"
keyNameMap["emergency_name"] = "Emergency Name"
keyNameMap["emergency_address"] = "Emergency Address"
keyNameMap["emergency_contact"] = "Emergency Contact"
keyNameMap["name"] = "Name"
keyNameMap["address"] = "Address"
keyNameMap["mobile"] = "Mobile"
keyNameMap["bank_address"]="Bank Address"


def check_each(dataa, asset):

    jsondic = {}
    if asset == 'fd':
        jsondic.update(fd(dataa))
    elif asset == 'bank_account':
        jsondic.update(bank_account(dataa))
    elif asset == 'mf':
        jsondic.update(mf(dataa))
    elif asset == 'locker':
        jsondic.update(locker(dataa))
    elif asset == 'indian_equity' or asset == 'us_equity' or asset == 'crypto' or asset == 'bonds' or asset == 'off_market':
        jsondic.update(indian_equity(dataa))
    elif asset == 'aif':
        jsondic.update(aif(dataa))
    elif asset == 'pms':
        jsondic.update(pms(dataa))
    elif asset == 'epf':
        jsondic.update(epf(dataa))
    elif asset == 'ppf':
        jsondic.update(ppf(dataa))
    elif asset == 'real_estate':
        jsondic.update(real_estate(dataa))
    elif asset == 'nps_tier1':
        jsondic.update(nps_tier1(dataa))
    elif asset == 'others':
        jsondic.update(others(dataa))
    elif asset == 'life_insurance':
        jsondic.update(life_insurance(dataa))
    elif asset == 'health_insurance':
        jsondic.update(health_insurance(dataa))
    elif asset == 'motor_insurance':
        jsondic.update(motor_insurance(dataa))
    else:
        jsondic.update(loan(dataa))
    return jsondic


def fd(dataa):
    jsondic = {}
    try:
        jsondic['bank_name'] = dataa['bank_name']
    except Exception as e:
        print(e)
        jsondic['bank_name'] = None
    try:
        jsondic['branch_address'] = dataa['branch_address']
    except Exception as e:
        print(e)
        jsondic['branch_address'] = None
    try:
        jsondic['deposit_name'] = dataa['deposit_name']
    except Exception as e:
        print(e)
        jsondic['deposit_name'] = None
    try:
        jsondic['amount_invested'] = dataa['amount_invested']
    except Exception as e:
        print(e)
        jsondic['amount_invested'] = None
    try:
        jsondic['investment_date'] = dataa['investment_date']
    except Exception as e:
        print(e)
        jsondic['investment_date'] = None
    try:
        jsondic['investment_duration'] = dataa['investment_duration']
    except Exception as e:
        print(e)
        jsondic['investment_duration'] = None
    try:
        jsondic['rate_interest'] = dataa['rate_interest']
    except Exception as e:
        print(e)
        jsondic['rate_interest'] = None
    try:
        jsondic['maturity_date'] = dataa['maturity_date']
    except Exception as e:
        print(e)
        jsondic['maturity_date'] = None
    try:
        jsondic['maturity_amount'] = dataa['maturity_amount']
    except Exception as e:
        print(e)
        jsondic['maturity_amount'] = None
    return jsondic


def mf(dataa):
    jsondic = {}
    try:
        jsondic['scheme_name'] = dataa['scheme_name']
    except Exception as e:
        print(e)
        jsondic['scheme_name'] = None
    try:
        jsondic['purchase_amount'] = dataa['purchase_amount']
    except Exception as e:
        print(e)
        jsondic['purchase_amount'] = None
    try:
        jsondic['puchase_nav'] = dataa['puchase_nav']
    except Exception as e:
        print(e)
        jsondic['puchase_nav'] = None
    try:
        jsondic['current_nav'] = dataa['current_nav']
    except Exception as e:
        print(e)
        jsondic['current_nav'] = None
    try:
        jsondic['current_amount'] = dataa['current_amount']
    except Exception as e:
        print(e)
        jsondic['current_amount'] = None
    try:
        jsondic['div_amount'] = dataa['div_amount']
    except Exception as e:
        print(e)
        jsondic['div_amount'] = None
    try:
        jsondic['gain_short'] = dataa['gain_short']
    except Exception as e:
        print(e)
        jsondic['gain_short'] = None
    try:
        jsondic['gain_long'] = dataa['gain_long']
    except Exception as e:
        print(e)
        jsondic['gain_long'] = None
    try:
        jsondic['return_abs'] = dataa['return_abs']
    except Exception as e:
        print(e)
        jsondic['return_abs'] = None
    try:
        jsondic['return_cagr'] = dataa['return_cagr']
    except Exception as e:
        print(e)
        jsondic['return_cagr'] = None
    return jsondic


def bank_account(dataa):
    jsondic = {}
    try:
        jsondic['bank_name'] = dataa['bank_name']
    except Exception as e:
        print(e)
        jsondic['bank_name'] = None
    try:
        jsondic['branch_address'] = dataa['branch_address']
    except Exception as e:
        print(e)
        jsondic['branch_address'] = None
    try:
        jsondic['account_holder'] = dataa['account_holder']
    except Exception as e:
        print(e)
        jsondic['account_holder'] = None
    try:
        jsondic['last_digit_account'] = dataa['last_digit_account']
    except Exception as e:
        print(e)
        jsondic['last_digit_account'] = None
    try:
        jsondic['account_type'] = dataa['account_type']
    except Exception as e:
        print(e)
        jsondic['account_type'] = None
    return jsondic


def locker(dataa):
    jsondic = {}
    try:
        jsondic['bank_name'] = dataa['bank_name']
    except Exception as e:
        print(e)
        jsondic['bank_name'] = None
    try:
        jsondic['bank_address'] = dataa['bank_address']
    except Exception as e:
        print(e)
        jsondic['bank_address'] = None
    try:
        jsondic['locker_number'] = dataa['locker_number']
    except Exception as e:
        print(e)
        jsondic['locker_number'] = None
    try:
        jsondic['locker_name'] = dataa['locker_name']
    except Exception as e:
        print(e)
        jsondic['locker_name'] = None
    return jsondic


def indian_equity(dataa):
    jsondic = {}
    try:
        jsondic['broker_name'] = dataa['broker_name']
    except Exception as e:
        print(e)
        jsondic['broker_name'] = None
    try:
        jsondic['account_number'] = dataa['account_number']
    except Exception as e:
        print(e)
        jsondic['account_number'] = None
    try:
        jsondic['instrument_name'] = dataa['instrument_name']
    except Exception as e:
        print(e)
        jsondic['instrument_name'] = None
    try:
        jsondic['qty'] = dataa['qty']
    except Exception as e:
        print(e)
        jsondic['qty'] = None

    try:
        jsondic['purchase_price'] = dataa['purchase_price']
    except Exception as e:
        print(e)
        jsondic['purchase_price'] = None
    try:
        jsondic['purchase_value'] = dataa['purchase_value']
    except Exception as e:
        print(e)
        jsondic['purchase_value'] = None
    try:
        jsondic['market_price'] = dataa['market_price']
    except Exception as e:
        print(e)
        jsondic['market_price'] = None
    try:
        jsondic['market_value'] = dataa['market_value']
    except Exception as e:
        print(e)
        jsondic['market_value'] = None
    return jsondic


def aif(dataa):
    jsondic = {}
    try:
        jsondic['amc_name'] = dataa['amc_name']
    except Exception as e:
        print(e)
        jsondic['amc_name'] = None
    try:
        jsondic['aif_name'] = dataa['aif_name']
    except Exception as e:
        print(e)
        jsondic['aif_name'] = None
    try:
        jsondic['interested_value'] = dataa['interested_value']
    except Exception as e:
        print(e)
        jsondic['interested_value'] = None
    try:
        jsondic['current_amount'] = dataa['current_amount']
    except Exception as e:
        print(e)
        jsondic['current_amount'] = None
    try:
        jsondic['account_number'] = dataa['account_number']
    except Exception as e:
        print(e)
        jsondic['account_number'] = None
    try:
        jsondic['invested_date'] = dataa['invested_date']
    except Exception as e:
        print(e)
        jsondic['invested_date'] = None
    try:
        jsondic['return_abs'] = dataa['return_abs']
    except Exception as e:
        print(e)
        jsondic['return_abs'] = None
    try:
        jsondic['return_cagr'] = dataa['return_cagr']
    except Exception as e:
        print(e)
        jsondic['return_cagr'] = None
    return jsondic


def pms(dataa):
    jsondic = {}
    try:
        jsondic['amc_name'] = dataa['amc_name']
    except Exception as e:
        print(e)
        jsondic['amc_name'] = None
    try:
        jsondic['pms_name'] = dataa['pms_name']
    except Exception as e:
        print(e)
        jsondic['pms_name'] = None
    try:
        jsondic['interested_value'] = dataa['interested_value']
    except Exception as e:
        print(e)
        jsondic['interested_value'] = None
    try:
        jsondic['current_amount'] = dataa['current_amount']
    except Exception as e:
        print(e)
        jsondic['current_amount'] = None
    try:
        jsondic['account_number'] = dataa['account_number']
    except Exception as e:
        print(e)
        jsondic['account_number'] = None
    try:
        jsondic['invested_date'] = dataa['invested_date']
    except Exception as e:
        print(e)
        jsondic['invested_date'] = None
    try:
        jsondic['return_abs'] = dataa['return_abs']
    except Exception as e:
        print(e)
        jsondic['return_abs'] = None
    try:
        jsondic['return_cagr'] = dataa['return_cagr']
    except Exception as e:
        print(e)
        jsondic['return_cagr'] = None
    return jsondic


def epf(dataa):
    jsondic = {}
    try:
        jsondic['epf_number'] = dataa['epf_number']
    except Exception as e:
        print(e)
        jsondic['epf_number'] = None
    try:
        jsondic['balance'] = dataa['balance']
    except Exception as e:
        print(e)
        jsondic['balance'] = None
    return jsondic


def ppf(dataa):
    jsondic = {}
    try:
        jsondic['bank_name'] = dataa['bank_name']
    except Exception as e:
        print(e)
        jsondic['bank_name'] = None
    try:
        jsondic['branch_address'] = dataa['branch_address']
    except Exception as e:
        print(e)
        jsondic['branch_address'] = None
    try:
        jsondic['ppf_account_number'] = dataa['ppf_account_number']
    except Exception as e:
        print(e)
        jsondic['ppf_account_number'] = None
    try:
        jsondic['balance'] = dataa['balance']
    except Exception as e:
        print(e)
        jsondic['balance'] = None
    return jsondic


def real_estate(dataa):
    jsondic = {}
    try:
        jsondic['property_name'] = dataa['property_name']
    except Exception as e:
        print(e)
        jsondic['property_name'] = None
    try:
        jsondic['invested_value'] = dataa['invested_value']
    except Exception as e:
        print(e)
        jsondic['invested_value'] = None
    try:
        jsondic['invested_date'] = dataa['invested_date']
    except Exception as e:
        print(e)
        jsondic['invested_date'] = None
    try:
        jsondic['current_value'] = dataa['current_value']
    except Exception as e:
        print(e)
        jsondic['current_value'] = None
    try:
        jsondic['property_type'] = dataa['property_type']
    except Exception as e:
        print(e)
        jsondic['property_type'] = None
    try:
        jsondic['property_size'] = dataa['property_size']
    except Exception as e:
        print(e)
        jsondic['property_size'] = None
    try:
        jsondic['location_pincode'] = dataa['location_pincode']
    except Exception as e:
        print(e)
        jsondic['location_pincode'] = None
    return jsondic


def nps_tier1(dataa):
    jsondic = {}
    try:
        jsondic['pran'] = dataa['pran']
    except Exception as e:
        print(e)
        jsondic['pran'] = None
    try:
        jsondic['current_balance'] = dataa['current_balance']
    except Exception as e:
        print(e)
        jsondic['current_balance'] = None
    try:
        jsondic['account_type'] = dataa['account_type']
    except Exception as e:
        print(e)
        jsondic['account_type'] = None
    try:
        jsondic['nominee_details'] = dataa['nominee_details']
    except Exception as e:
        print(e)
        jsondic['nominee_details'] = None
    try:
        jsondic['bank_details'] = dataa['bank_details']
    except Exception as e:
        print(e)
        jsondic['bank_details'] = None
    return jsondic


def others(dataa):
    jsondic = {}
    try:
        jsondic['instrument_name'] = dataa['instrument_name']
    except Exception as e:
        print(e)
        jsondic['instrument_name'] = None
    try:
        jsondic['purchase_value'] = dataa['purchase_value']
    except Exception as e:
        print(e)
        jsondic['purchase_value'] = None
    try:
        jsondic['current_value'] = dataa['current_value']
    except Exception as e:
        print(e)
        jsondic['current_value'] = None
    try:
        jsondic['purchase_date'] = dataa['purchase_date']
    except Exception as e:
        print(e)
        jsondic['purchase_date'] = None
    try:
        jsondic['units'] = dataa['units']
    except Exception as e:
        print(e)
        jsondic['units'] = None
    return jsondic


def life_insurance(dataa):
    jsondic = {}
    try:
        jsondic['plan_name'] = dataa['plan_name']
    except Exception as e:
        print(e)
        jsondic['plan_name'] = None
    try:
        jsondic['policy_number'] = dataa['policy_number']
    except Exception as e:
        print(e)
        jsondic['policy_number'] = None
    try:
        jsondic['policy_holder_name'] = dataa['policy_holder_name']
    except Exception as e:
        print(e)
        jsondic['policy_holder_name'] = None
    try:
        jsondic['sum_assured'] = dataa['sum_assured']
    except Exception as e:
        print(e)
        jsondic['sum_assured'] = None
    try:
        jsondic['policy_start_date'] = dataa['policy_start_date']
    except Exception as e:
        print(e)
        jsondic['policy_start_date'] = None
    try:
        jsondic['policy_maturity_date'] = dataa['policy_maturity_date']
    except Exception as e:
        print(e)
        jsondic['policy_maturity_date'] = None
    try:
        jsondic['policy_term'] = dataa['policy_term']
    except Exception as e:
        print(e)
        jsondic['policy_term'] = None
    try:
        jsondic['premium_payment'] = dataa['premium_payment']
    except Exception as e:
        print(e)
        jsondic['premium_payment'] = None
    try:
        jsondic['premium_amount'] = dataa['premium_amount']
    except Exception as e:
        print(e)
        jsondic['premium_amount'] = None
    try:
        jsondic['premium_payment_frequency'] = dataa['premium_payment_frequency']
    except Exception as e:
        print(e)
        jsondic['premium_payment_frequency'] = None
    try:
        jsondic['renewal_date'] = dataa['renewal_date']
    except Exception as e:
        print(e)
        jsondic['renewal_date'] = None

    return jsondic


def health_insurance(dataa):
    jsondic = {}
    try:
        jsondic['plan_name'] = dataa['plan_name']
    except Exception as e:
        print(e)
        jsondic['plan_name'] = None
    try:
        jsondic['policy_number'] = dataa['policy_number']
    except Exception as e:
        print(e)
        jsondic['policy_number'] = None
    try:
        jsondic['policy_holder_name'] = dataa['policy_holder_name']
    except Exception as e:
        print(e)
        jsondic['policy_holder_name'] = None
    try:
        jsondic['sum_assured'] = dataa['sum_assured']
    except Exception as e:
        print(e)
        jsondic['sum_assured'] = None
    try:
        jsondic['policy_start_date'] = dataa['policy_start_date']
    except Exception as e:
        print(e)
        jsondic['policy_start_date'] = None
    try:
        jsondic['family_member_covered'] = dataa['family_member_covered']
    except Exception as e:
        print(e)
        jsondic['family_member_covered'] = None
    try:
        jsondic['tpa_account_number'] = dataa['tpa_account_number']
    except Exception as e:
        print(e)
        jsondic['tpa_account_number'] = None
    try:
        jsondic['company_number'] = dataa['company_number']
    except Exception as e:
        print(e)
        jsondic['company_number'] = None
    try:
        jsondic['premium_amount'] = dataa['premium_amount']
    except Exception as e:
        print(e)
        jsondic['premium_amount'] = None
    try:
        jsondic['renewal_date'] = dataa['renewal_date']
    except Exception as e:
        print(e)
        jsondic['renewal_date'] = None

    return jsondic


def motor_insurance(dataa):
    jsondic = {}
    try:
        jsondic['plan_name'] = dataa['plan_name']
    except Exception as e:
        print(e)
        jsondic['plan_name'] = None
    try:
        jsondic['policy_number'] = dataa['policy_number']
    except Exception as e:
        print(e)
        jsondic['policy_number'] = None
    try:
        jsondic['policy_holder_name'] = dataa['policy_holder_name']
    except Exception as e:
        print(e)
        jsondic['policy_holder_name'] = None
    try:
        jsondic['insured_declared_value'] = dataa['insured_declared_value']
    except Exception as e:
        print(e)
        jsondic['insured_declared_value'] = None
    try:
        jsondic['policy_start_date'] = dataa['policy_start_date']
    except Exception as e:
        print(e)
        jsondic['policy_start_date'] = None
    try:
        jsondic['type_start_cover'] = dataa['type_start_cover']
    except Exception as e:
        print(e)
        jsondic['type_start_cover'] = None
    try:
        jsondic['company_helpdesk_number'] = dataa['company_helpdesk_number']
    except Exception as e:
        print(e)
        jsondic['company_helpdesk_number'] = None
    try:
        jsondic['premium_amount'] = dataa['premium_amount']
    except Exception as e:
        print(e)
        jsondic['premium_amount'] = None
    try:
        jsondic['renewal_date'] = dataa['renewal_date']
    except Exception as e:
        print(e)
        jsondic['renewal_date'] = None

    return jsondic


def loan(dataa):
    jsondic = {}
    try:
        jsondic['bank_name'] = dataa['bank_name']
    except Exception as e:
        print(e)
        jsondic['bank_name'] = None
    try:
        jsondic['loan_account_number'] = dataa['loan_account_number']
    except Exception as e:
        print(e)
        jsondic['loan_account_number'] = None
    try:
        jsondic['loan_amount'] = dataa['loan_amount']
    except Exception as e:
        print(e)
        jsondic['loan_amount'] = None
    try:
        jsondic['asset_name'] = dataa['asset_name']
    except Exception as e:
        print(e)
        jsondic['asset_name'] = None
    try:
        jsondic['asset_type'] = dataa['asset_type']
    except Exception as e:
        print(e)
        jsondic['asset_type'] = None
    try:
        jsondic['loan_start_date'] = dataa['loan_start_date']
    except Exception as e:
        print(e)
        jsondic['loan_start_date'] = None
    try:
        jsondic['loan_tenure'] = dataa['loan_tenure']
    except Exception as e:
        print(e)
        jsondic['loan_tenure'] = None
    try:
        jsondic['emi_amount'] = dataa['emi_amount']
    except Exception as e:
        print(e)
        jsondic['emi_amount'] = None
    try:
        jsondic['roi'] = dataa['roi']
    except Exception as e:
        print(e)
        jsondic['roi'] = None
    try:
        jsondic['ecs_bank'] = dataa['ecs_bank']
    except Exception as e:
        print(e)
        jsondic['ecs_bank'] = None
    try:
        jsondic['insurance_protect_loan'] = dataa['insurance_protect_loan']
    except Exception as e:
        print(e)
        jsondic['insurance_protect_loan'] = None
    try:
        jsondic['current_outstanding'] = dataa['current_outstanding']
    except Exception as e:
        print(e)
        jsondic['current_outstanding'] = None
    return jsondic


def fetch_json(typeId):
    dat = dataJson.objects.filter(status=ACTIVE, typeId=typeId)
    dat = dataJsonSerializer(dat, many=True)
    return dat.data[0]


def getuserloggedin(userid):
    print("inside func")
    user = TblUsers.objects.filter(id=userid)
    user = TblUsersSerializer(user, many=True)
    print(user.data[0])
    if (user.data[0] != None):
        return user.data[0]['fundlogin']
    else:
        return 0


def slugGenerator(inp):
    words = inp.split()
    number = random.randint(1000, 9999)
    words.append(str(number))
    return "-".join(words)


def tokenGenerator():

    return uuid4()


s3 = boto3.client(
    's3',
    aws_access_key_id=settings.ACCESS_KEY,
    aws_secret_access_key=settings.SECRET_ACCESS_KEY,
)


def uploadFile(userId, writer):
    user = userId
    filename = filename = os.path.join("assetRecorder", f"{user}.xlsx")
    uploadFileName = f"assetexcel/{userId}.xlsx"
    fullFilePath = "https://stocktick1.s3.ap-south-1.amazonaws.com/" + uploadFileName
    try:
        s3.upload_file(filename, 'stocktick1', uploadFileName)
        try:
            cwd = os.path.dirname(__file__)
            writer.close()
            os.remove(os.path.join(cwd, f"{user}.xlsx"))
            print("files deleted")
        except Exception as e:
            print("delete nahi hui")
            print(e)
        return fullFilePath
    except Exception as e:  # getting permission denied error
        print(e)
        return False
    return True

# def format_color_groups(df):
#     colors = ['gold', 'lightblue']
#     x = df.copy()
#     factors = list(x['publication'].unique())
#     i = 0
#     for factor in factors:
#         style = f'background-color: {colors[i]}'
#         x.loc[x['publication'] == factor, :] = style
#         i = not i
#     return x
