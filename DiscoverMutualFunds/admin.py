from django.contrib import admin
from .models import *

@admin.register(DMFCatg)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'catg_name','status')


@admin.register(DMFunds)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'fund_name','status')

@admin.register(clickcount)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'fund_id','status')


@admin.register(ExploreMFCatg)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'fund_catg_name','status')


@admin.register(ExploreMFunds)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id', 'fund_name','status')


