from distutils.util import execute
import json
from os import remove
# from tkinter import Exception
from urllib import response

from users.auth import verifyUser

# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
# from itsdangerous import Serializer
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings

from uuid import uuid4
import pytz
import datetime
from .models import *

from .utils import tokenGenerator, getuserloggedin
from users.constant import ROW_STATUS, ACTIVE, LOCKED

from .serializers import *


class DMFCatgAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None
        try:
            catg = DMFCatg.objects.filter(status=ACTIVE)
            catg = DMFCatgSerializer(catg, many=True)

            # checking if user is logged into fundbazar
            if (userId == None or getuserloggedin(userId) == False):

                for i in range(len(catg.data)):

                    if (catg.data[i]["lock_status"] == 'LOCKED'):

                        En_Value = catg.data[i]['icon']
                        catg.data[i].clear()
                        catg.data[i]['icon'] = En_Value
                        catg.data[i]['lock_status'] = "LOCKED"

                return Response(catg.data, status=status.HTTP_201_CREATED)
            return Response(catg.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)


class ExploreMFCatgAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None
        try:
            catg = ExploreMFCatg.objects.filter(status=ACTIVE)
            catg = ExploreMFCatgSerializer(catg, many=True)

            # checking if user is logged into fundbazar
            if (userId == None or getuserloggedin(userId) == False):

                for i in range(len(catg.data)):

                    if (catg.data[i]["lock_status"] == 'LOCKED'):

                        En_Value = catg.data[i]['icon']
                        catg.data[i].clear()
                        catg.data[i]['icon'] = En_Value
                        catg.data[i]['lock_status'] = "LOCKED"

                return Response(catg.data, status=status.HTTP_201_CREATED)
            return Response(catg.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)


class DMFundsAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            catg = request.query_params["catg_id"]
        except Exception as e:
            return Response({"msg": "Category Id not found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None
        try:
            users = DMFunds.objects.filter(status=ACTIVE, catg_id=catg)
            users = DMFundsSerializer(users, many=True)

            if (userId == None or getuserloggedin(userId) == False):
                # checking if user is logged into fundbazar
                print("inside loggedin")

                for i in range(len(users.data)):
                    print(users.data[i]["lock_status"])

                    if (users.data[i]["lock_status"] == LOCKED):
                        print("inside locked")

                        En_Value = users.data[i]['icon']
                        users.data[i].clear()
                        users.data[i]['icon'] = En_Value
                        users.data[i]['lock_status'] = "LOCKED"

                return Response(users.data, status=status.HTTP_201_CREATED)
            return Response(users.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)


class ExploreMFundsAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            catg = request.query_params["catg_id"]
        except Exception as e:
            return Response({"msg": "Category Id not found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                token = None
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                userId = None
        except Exception as e:
            userId = None
        try:
            users = ExploreMFunds.objects.filter(status=ACTIVE, catg_id=catg)
            users = ExploreMFundsSerializer(users, many=True)

            if (userId == None or getuserloggedin(userId) == False):
                # checking if user is logged into fundbazar
                print("inside loggedin")

                for i in range(len(users.data)):

                    if (users.data[i]["lock_status"] == 'LOCKED'):

                        En_Value = users.data[i]['icon']
                        users.data[i].clear()
                        users.data[i]['icon'] = En_Value
                        users.data[i]['lock_status'] = "LOCKED"

                return Response(users.data, status=status.HTTP_201_CREATED)
            return Response(users.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)


class ExploreMFundsSyncAPIView(APIView):

    def get(self, request):
        data = request.data

        try:
            fundsList = ExploreMFunds.objects.filter(status=ACTIVE)
            fundsList = ExploreMFundsSerializer(fundsList, many=True)

            updateList = []

            for obj in fundsList.data:
                print(obj['fetch_url'])
                # fetch teh data
                obj['oneday'] = "4.3"
                obj['oneyear'] = "9.3"
                obj['threeyear'] = "10.3"
                updateList.append(obj)
                ExploreMFunds.objects.filter(id=obj['id']).update(
                    oneday=4.3, oneyear=9.3, threeyear=10.3)

            fundsList2 = DMFunds.objects.filter(status=ACTIVE)
            fundsList2 = DMFundsSerializer(fundsList2, many=True)

            for obj in fundsList2.data:
                print(obj['fetch_url'])
                # fetch teh data
                obj['oneday'] = "4.3"
                obj['oneyear'] = "9.3"
                obj['threeyear'] = "10.3"
                updateList.append(obj)
                DMFunds.objects.filter(id=obj['id']).update(
                    oneday=4.3, oneyear=9.3, threeyear=10.3)

            print(updateList)
            # ExploreMFunds.objects.bulk_update(updateList,['oneday', 'oneyear','threeyear'])

            return Response(updateList, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"msg": "Error syncing data"}, status=status.HTTP_404_NOT_FOUND)


class clickcountAPIView(APIView):

    def get(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            users = clickcount.objects.filter(user_id=userId)
            users = clickcountSerializer(users, many=True)
            return Response(users.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        data = request.data
        try:
            token = request.headers["authToken"]
        except Exception as e:
            try:
                token = request.query_params["authToken"]
            except Exception as e:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        catg_id = data["catg_id"]
        fund_id = data["fund_id"]
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        print("check", userId)
        try:
            exists = clickcount.objects.get(
                user_id=userId, catg_id=catg_id, fund_id=fund_id)
        except:

            exists = -1

        if (exists != -1):
            exists.count = exists.count+1
            exists.status = ACTIVE
            exists.save()
            return Response({"message": "Click count Updated"}, status=status.HTTP_201_CREATED)

        else:
            print("check2")
            if data:
                print("check3")
                data["created_by"] = userId
                data["modified_by"] = userId
                data["user_id"] = userId
                data["count"] = 1

            serializer = clickcountSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class mutualFundsQueryAPIView(APIView):

    def post(self, request):
        data = request.data
        if data:
            data['status'] = ACTIVE

        serializer = MutualFundsQuerySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
