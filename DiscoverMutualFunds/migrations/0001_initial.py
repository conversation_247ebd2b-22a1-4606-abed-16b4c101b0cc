# Generated by Django 4.0.3 on 2022-03-30 17:38

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='clickcount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('user_id', models.IntegerField()),
                ('catg_id', models.IntegerField()),
                ('fund_id', models.IntegerField()),
                ('count', models.IntegerField(default=1)),
                ('last_access', models.DateTime<PERSON>ield(auto_now=True, null=True)),
                ('status', models.Char<PERSON>ield(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_click_count',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DMFCatg',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('catg_name', models.TextField(blank=True, max_length=50, null=True)),
                ('icon', models.TextField(max_length=100)),
                ('debt', models.IntegerField(blank=True, null=True)),
                ('equity', models.IntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('lock_status', models.CharField(choices=[('LOCKED', 'LOCKED'), ('UNLOCKED', 'UNLOCEKD')], default='LOCKED', max_length=10)),
            ],
            options={
                'db_table': 'tbl_dmf_catg',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DMFunds',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('fund_name', models.TextField(max_length=500)),
                ('icon', models.TextField(max_length=100)),
                ('oneday', models.IntegerField(unique=True)),
                ('oneyear', models.IntegerField(unique=True)),
                ('threeyear', models.IntegerField(unique=True)),
                ('redirect_url', models.TextField(max_length=150)),
                ('fetch_url', models.TextField(max_length=150)),
                ('catg_id', models.IntegerField(unique=True)),
                ('weightage', models.IntegerField(blank=True, null=True)),
                ('last_sync', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
            ],
            options={
                'db_table': 'tbl_dm_funds',
                'managed': False,
            },
        ),
    ]
