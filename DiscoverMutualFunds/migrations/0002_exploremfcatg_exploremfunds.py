# Generated by Django 4.0.4 on 2022-07-10 05:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('DiscoverMutualFunds', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExploreMFCatg',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('fund_catg_name', models.TextField(blank=True, max_length=50, null=True)),
                ('icon', models.TextField(max_length=100)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('lock_status', models.CharField(choices=[('LOCKED', 'LOCKED'), ('UNLOCKED', 'UNLOCKED')], default='UNLOCKED', max_length=10)),
            ],
            options={
                'db_table': 'tbl_explore_funds_catg',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='ExploreMFunds',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('fund_name', models.TextField(max_length=500)),
                ('long_desc', models.TextField(blank=True, max_length=500, null=True)),
                ('icon', models.TextField(max_length=100)),
                ('oneday', models.IntegerField(unique=True)),
                ('oneyear', models.IntegerField(unique=True)),
                ('threeyear', models.IntegerField(unique=True)),
                ('redirect_url', models.TextField(max_length=500)),
                ('fetch_url', models.TextField(max_length=500)),
                ('catg_id', models.IntegerField()),
                ('catg_name', models.TextField(max_length=100)),
                ('weightage', models.IntegerField(blank=True, null=True)),
                ('last_sync', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('lock_status', models.CharField(choices=[('LOCKED', 'LOCKED'), ('UNLOCKED', 'UNLOCKED')], default='LOCKED', max_length=10)),
            ],
            options={
                'db_table': 'tbl_explore_funds',
                'managed': False,
            },
        ),
    ]
