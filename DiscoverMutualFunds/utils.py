import json
# Create your views here.

import random
from uuid import uuid4
from users.models import TblUsers
from users.serializer import TblUsersSerializer


from users.constant import ACTIVE


def getuserloggedin(userid):
    print("inside func")
    user=TblUsers.objects.filter(id=userid)
    user= TblUsersSerializer(user,many=True)
    print(user.data[0])
    if(user.data[0]!=None):
        return user.data[0]['fundlogin']
    else:
        return 0

def slugGenerator(inp):
    words=inp.split()
    number = random.randint(1000, 9999)
    words.append(str(number))
    return "-".join(words)

def tokenGenerator():

    return uuid4()

