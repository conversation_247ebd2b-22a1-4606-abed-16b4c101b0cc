from rest_framework import serializers

from .models import DMFunds,DMFCatg, ExploreMFCatg, ExploreMFunds, MutualFundsQuery,clickcount


class DMFundsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DMFunds
        fields = "__all__"

class DMFCatgSerializer(serializers.ModelSerializer):
    class Meta:
        model = DMFCatg
        fields = "__all__"

class clickcountSerializer(serializers.ModelSerializer):
    class Meta:
        model = clickcount
        fields = "__all__"

class ExploreMFCatgSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExploreMFCatg
        fields = "__all__"

class ExploreMFundsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExploreMFunds
        fields = "__all__"


class MutualFundsQuerySerializer(serializers.ModelSerializer):
    class Meta:
        model = MutualFundsQuery
        fields = "__all__"

