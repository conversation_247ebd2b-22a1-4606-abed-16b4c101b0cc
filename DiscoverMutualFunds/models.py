from lib2to3.pgen2 import token
from math import fabs
from django.db import models
from users.constant import ROW_STATUS, ACTIVE, ROW_LOCK, LOCKED, UNLOCKED, INACTIVE

# Create your models here.


class DMFCatg(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    catg_name = models.TextField(max_length=50, blank=True, null=True)
    icon = models.TextField(max_length=100, blank=False, null=False)
    debt = models.IntegerField(blank=True, null=True)
    equity = models.IntegerField(blank=True, null=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    lock_status = models.Char<PERSON>ield(
        blank=False, null=False, choices=ROW_LOCK, default=UNLOCKED, max_length=10)
    description = models.TextField(max_length=2000, blank=True, null=True)

    def __str__(self):
        return str(self.catg_name)

    class Meta:
        managed = False
        db_table = 'tbl_dmf_catg'


class ExploreMFCatg(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    fund_catg_name = models.TextField(max_length=50, blank=True, null=True)
    icon = models.TextField(max_length=100, blank=False, null=False)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    lock_status = models.CharField(
        blank=False, null=False, choices=ROW_LOCK, default=UNLOCKED, max_length=10)

    def __str__(self):
        return str(self.fund_catg_name)

    class Meta:
        managed = False
        db_table = 'tbl_explore_funds_catg'


class DMFunds(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    fund_name = models.TextField(max_length=500, blank=False, null=False)
    long_desc = models.TextField(max_length=500, blank=True, null=True)
    icon = models.TextField(max_length=100, blank=False, null=False)
    oneday = models.FloatField(unique=True, blank=False, null=False)
    oneyear = models.FloatField(unique=True, blank=False, null=False)
    threeyear = models.FloatField(unique=True, blank=False, null=False)
    redirect_url = models.TextField(max_length=150, blank=False, null=False)
    fetch_url = models.TextField(max_length=150, blank=False, null=False)
    catg_id = models.IntegerField(blank=False, null=False)
    catg_name = models.TextField(max_length=100, blank=False, null=False)
    weightage = models.IntegerField(blank=True, null=True)
    last_sync = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    lock_status = models.CharField(
        blank=False, null=False, choices=ROW_LOCK, default=UNLOCKED, max_length=10)

    def __str__(self):
        return str(self.fund_name)

    class Meta:
        managed = False
        db_table = 'tbl_dm_funds'


class ExploreMFunds(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    fund_name = models.TextField(max_length=500, blank=False, null=False)
    long_desc = models.TextField(max_length=500, blank=True, null=True)
    icon = models.TextField(max_length=100, blank=False, null=False)
    oneday = models.FloatField(unique=True, blank=False, null=False)
    oneyear = models.FloatField(unique=True, blank=False, null=False)
    threeyear = models.FloatField(unique=True, blank=False, null=False)
    redirect_url = models.TextField(max_length=500, blank=False, null=False)
    fetch_url = models.TextField(max_length=500, blank=False, null=False)
    catg_id = models.IntegerField(blank=False, null=False)
    catg_name = models.TextField(max_length=100, blank=False, null=False)
    weightage = models.IntegerField(blank=True, null=True)
    last_sync = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    lock_status = models.CharField(
        blank=False, null=False, choices=ROW_LOCK, default=LOCKED, max_length=10)

    def __str__(self):
        return str(self.fund_name)

    class Meta:
        managed = False
        db_table = 'tbl_explore_funds'


class MutualFundsQuery(models.Model):
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    query = models.TextField(max_length=5000, blank=False, null=False)
    email = models.TextField(max_length=75, blank=True, null=True)
    phone = models.TextField(max_length=15, blank=False, null=False)
    name = models.TextField(max_length=80, blank=False, null=False)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)

    def __str__(self):
        return str(self.name) + " " + str(self.phone)

    class Meta:
        managed = False
        db_table = 'tbl_mutual_funds_query'


class clickcount(models.Model):
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    user_id = models.IntegerField(blank=False, null=False)
    catg_id = models.IntegerField(blank=False, null=False)
    fund_id = models.IntegerField(blank=False, null=False)
    count = models.IntegerField(default=1, blank=False, null=False)
    last_access = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)

    def __str__(self):
        return str(self.user_id)+str(self.fund_id)

    class Meta:
        managed = False
        db_table = 'tbl_click_count'
