from django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
# from rest_framework.routers import DefaultRouterfrom django.contrib import admin
from django.urls import path, include
# from .views import article_list, article_detail, ArticleAPIView, ArticleDetailsAPIView, GenericAPIView, ArticleViewSet
from rest_framework.routers import DefaultRouter


from .views import *


urlpatterns = [
    # path('article/', article_list), # Function based calls
    path('catg/', DMFCatgAPIView.as_view()),
    path('funds/', DMFundsAPIView.as_view()),
    path('explore-catg/', ExploreMFCatgAPIView.as_view()),
    path('explore-funds/', ExploreMFundsAPIView.as_view()),
    path('count/', clickcountAPIView.as_view()),
    path('sync/', ExploreMFundsSyncAPIView.as_view()),
    path('mf-queries/', mutualFundsQueryAPIView.as_view()),

    # path('result/', RangeResultAPIView.as_view()),


]
