## Deploy to EC2

1. SSH into ec2 instance
2. type `` cd stocktick-backend``
3. pull latest code from repo 
4. Run command ``` sudo supervisorctl restart guni:gunicorn ``` to restart Django server running.
5. Run ``` sudo service nginx restart  ``` to restart nginx


# Frontend

 - [1] Login to AWS Console
 - [2] Login to EC2 console
 - [3] locate the folder ( either in base dir or in /opt/ dir)
 - [4] cd into the folder
 - [5] `git pull origin`
    - [6] `npm install`
    - [7] `sudo npm audit fix`/`sudo npm audit fix --force`
    - [7] `sudo npm run build`
    - [8] `sudo systemctl restart nginx`

# Backend
    - [1] Login to AWS Console
    - [2] <PERSON>gin to EC2 console
    - [3] locate the folder ( either in base dir or in /opt/ dir)
    - [4] cd into the folder
    - [5] `git pull origin`
    - [6] `screen -r`
    - Debug steps server terminates
    - [7] `ctrl+c`
    - [8] `pip install -r requirements.txt`
    - [9] `python manage.py runserver 0:4000` or `python manage.py runserver 0:8000`