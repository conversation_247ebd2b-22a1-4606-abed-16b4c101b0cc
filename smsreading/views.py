from django.shortcuts import render

# Create your views here.
import json
# Create your views here.

from django.db import connection
from django.shortcuts import render
from django.http import Http404, JsonResponse
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core import serializers
from django.conf import settings

from uuid import uuid4
import pytz
import datetime


def getTokenExpiryTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    now = datetime.datetime.now(IST) + datetime.timedelta(days=30)
    print(now)
    return str(now)[:-6]


def getCurrentTimestamp():
    IST = pytz.timezone('Asia/Kolkata')
    return str(datetime.datetime.now(IST))[:-6]
# Create your views here.


def verifyUser(token):
    conn = connection
    cur = conn.cursor()

    now = getCurrentTimestamp()
    token_expiry = getTokenExpiryTimestamp()
    q = "SELECT user_id,token_expiry FROM tbl_user_sessions WHERE token = '%s'" % token
    cur.execute(q)
    res = cur.fetchone()
    print(q)

    if res:
        # USER_ID = res['id']
        if str(res[1]) < now:
            cur.close()
            # conn.close()
            return {"message": False, "reason": "Token Expired"}

        USER_ID = res[0]
        update = "UPDATE tbl_user_sessions SET token_last_used = '%s', token_expiry = '%s' WHERE user_id = %s " % (
            now, token_expiry, res[0])
        cur.execute(update)
        conn.commit()
        print(update)
        cur.close()
        # conn.close()

        return {"message": True, "id": res[0], "reason": "Success"}
    else:
        cur.close()
        # conn.close()
        return {"message": False, "reason": "Token Not Found"}


@api_view(["GET"])
def getDataLastSynced(req):
    # try:
    try:
        token = req.headers["authToken"]
        response = verifyUser(token)
        if response["message"]:
            user_id = response['id']
            print(user_id, 'hola')
        else:
            return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

    except Exception as e:
        print(e.args[0])
        return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

    conn = connection
    cur = conn.cursor()

    q = "SELECT credit,debit,agent,date_transaction,last_synced FROM `stocktick-qa`.tbl_sms_data WHERE user_id = '%s' AND status = 'ACTIVE'" % user_id
    cur.execute(q)
    res1 = cur.fetchall()
    print(res1)
    if not res1:
        date = datetime.datetime.fromtimestamp(1609464000)
        return JsonResponse({"last_synced": str(date)}, safe=False)
    res = []
    for r in res1:
        dic = {}
        dic['credit'] = str(r[0])
        dic['debit'] = str(r[1])
        dic['agent'] = r[2]
        dic['date_transaction'] = r[3]
        dic['last_synced'] = r[4]
        res.append(dic)

    res = sorted(res, key=lambda d: d['last_synced'], reverse=True)
    last_synced = res[0]['last_synced']
    final = {"last_synced": last_synced, "data": res}

    return JsonResponse(res, safe=False)
    # except Exception as e:
    #     print(e.args[0])
    #     return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def postsmsdata(req):
    try:
        try:
            token = req.headers["authToken"]
            response = verifyUser(token)
            if response["message"]:
                user_id = response['id']
                print(user_id, 'hola')
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        params = json.loads(req.body)
        if not params:
            return Response({"message": "Input not provided"}, status=status.HTTP_400_BAD_REQUEST)
        req = []
        for p in params:
            req.append({"type": p["type"], "amount": p["amount"],
                       "agent": p["agent"], "date_transaction": p["date_transaction"]})

        print(req)
        conn = connection
        cur = conn.cursor()

        now = getCurrentTimestamp()

        # Prepare insert query

        insert_q = "INSERT INTO `stocktick-qa`.tbl_sms_data (created_by, status, credit,debit,agent, date_transaction, user_id,last_synced) VALUES "
        for r in req:
            if(r["type"] == "debit"):
                insert_q += "('%s','ACTIVE' ,%s , %s , '%s' , '%s' ,%s, '%s')," % (user_id,
                                                                                   '0.0', r['amount'], r['agent'], r['date_transaction'], user_id, now)
            else:
                insert_q += "('%s','ACTIVE', %s , %s , '%s' , '%s' ,%s, '%s')," % (user_id,
                                                                                   r['amount'], '0.0', r['agent'], r['date_transaction'], user_id, now)

        insert_q = insert_q[:-1]
        print(insert_q)

        cur.execute(insert_q)
        conn.commit()

        return JsonResponse({"message": "Added sms data"})

    except Exception as e:
        print(e.args)
        return Response({"message": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
