import matplotlib.pyplot as plt
import os
from cProfile import label
from operator import ne
from datetime import date, datetime
import pytz
import random
# from tkinter import CENTER
# from turtle import color
from uuid import uuid4

from .models import *
from .serializer import *

from users.constant import ACTIVE
from .css import CSS
import pdfkit
import boto3
from django.conf import settings
import matplotlib
matplotlib.use('Agg')

s3 = boto3.client(
    's3',
    aws_access_key_id=settings.ACCESS_KEY,
    aws_secret_access_key=settings.SECRET_ACCESS_KEY,
)


def cashflowBreakup(userId):
    plt.clf()
    total = fpQ2.objects.filter(created_by=userId)
    s = fpQ2Serializer(total, many=True)
    s = s.data[0]
    lbl = ['Investment Amount', 'Household Expense',
           'Lifestyle Expense', 'Surplus', 'Tax Paid', 'EMI Paid']
    data = [s['invst_amount'], s['household_expns'],
            s['lifestyle_expns'], s['surplus'], s['tax_paid'], s['emi_paid']]
    plt.pie(data, labels=lbl, autopct="%1.1f%%", radius=1.5,
            wedgeprops={'linewidth': 3.0, 'edgecolor': 'white'})
    plt.axis('equal')

    try:
        plt.savefig(f"finantialPlanning/{userId}c.png")
        return f"./{userId}c.png"
    except Exception as e:
        return 0


def assetAllocation(userId):
    total = fpQ3.objects.filter(created_by=userId)
    s = fpQ3Serializer(total, many=True)
    s = s.data[0]
    plt.clf()
    lbl = ['Gold', 'Equity', 'Real Estate', 'Debt']

    data = [s['gold'], s['equity'], s['estate'], s['debt']]
    plt.figure(figsize=(12, 10))
    plt.pie(data, labels=lbl, autopct="%1.1f%%", pctdistance=0.75, radius=1.25, wedgeprops={'linewidth': 3.0, 'edgecolor': 'white'},
            textprops={'size': 'xx-large'})
    centre_circle = plt.Circle((0, 0), 0.70, fc='white')
    fig = plt.gcf()

    fig.gca().add_artist(centre_circle)
    plt.axis('equal')
    try:
        plt.savefig(f"finantialPlanning/{userId}a.png")
        return f"../{userId}a.png"
    except Exception as e:
        return 0


def financialInstrument(userId):
    total = fpQ4.objects.filter(created_by=userId)
    s = fpQ4Serializer(total, many=True)
    s = s.data[0]
    plt.clf()
    lbl = ['Fixed Deposits', 'Mutual Funds', 'EPF/PPF/NPS', 'Shares']

    data = [s['md'], s['mf'], s['nps'], s['shares']]
    matplotlib.rcParams.update({'font.size': 16})

    fig, ax = plt.subplots(figsize=(15, 10))
    bars = ax.barh(lbl, data, height=0.5, color='orange')
    for i, v in enumerate(data):
        ax.text(v + 3, i, str(v),
                color='black', fontweight='bold',)

    # Hide axes ticks
    ax.set_xticks([])
    plt.yticks(rotation=17)

    try:
        plt.savefig(f"finantialPlanning/{userId}f.png")
        return f"./{userId}f.png"
    except Exception as e:
        return 0


def networkHistory(userId):
    total = fpQ8.objects.filter(created_by=userId)
    matplotlib.rcParams.update({'font.size': 16})
    s = fpQ8Serializer(total, many=True)
    s = s.data[0]
    plt.clf()
    asset = s['assets'].split('|')
    asset = list(map(int, asset))
    liability = s['liability'].split('|')
    liability = list(map(int, liability))
    networth = s['net_worth'].split('|')
    networth = list(map(int, networth))
    year = s['years'].split('|')
    plt.plot(year, liability, color='orange', linewidth=3)
    plt.plot(year, asset, linewidth=3)

    plt.plot(year, networth, color='grey', linewidth=3)

    leg = plt.legend(['liability', 'asset', 'networth'], fontsize=18)

    leg_lines = leg.get_lines()
    leg_texts = leg.get_texts()
    # bulk-set the properties of all lines and texts
    plt.setp(leg_lines, linewidth=6)
    # plt.setp(leg_texts, fontsize='x-large')

    try:
        plt.savefig(f"finantialPlanning/{userId}n.png")
        return f"./{userId}n.png"
    except Exception as e:
        return 0


def getImgUrl(userId):
    imgUrl = fpQ7.objects.filter(created_by=userId)
    if (imgUrl):
        return imgUrl[0].img
    return "https://stocktick1.s3.ap-south-1.amazonaws.com/financialpdf/images/family.png"


def getChildMarriageCalc(userId):
    data = fpChildMarriageCalc.objects.filter(created_by=userId)
    if (data):
        return data[0]
    return {
        "spend": "NA",
        "inflation_rate": "NA",
        "age": "NA",
        "marriage_age": "NA",
        "investment_return": "NA",
        "child_marriage_cost": "NA",
        "monthly_investment": "NA"
    }


def getEmergencyFundCalc(userId):
    data = fpEmergencyFundCalc.objects.filter(created_by=userId)
    if (data):
        return data[0]
    return {
        "living_expense": "NA",
        "emergency_rate": "NA",
        "emergency_fund": "NA",
        "investment_return": "NA",
        "emergency_fund_you_need": "NA",
        "monthly_investment_reqd": "NA"
    }


def getChildEduCalc(userId):
    data = fpChildEduCalc.objects.filter(created_by=userId)
    if (data):
        return data[0]
    return {
        "current_edu_cost": "NA",
        "inflation_rate": "NA",
        "current_age": "NA",
        "corpus_age": "NA",
        "expected_investment_return": "NA",
        "future_edu_cost": "NA",
        "monthly_investment_reqd": "NA"
    }


def getRetirementCalc(userId):
    data = fpRetirementCalc.objects.filter(created_by=userId)
    # print("Retirement.........")
    # print(data)
    if (data):
        print(data[0])
        return data[0]
    return {
        "age": "NA",
        "retirement_type": "NA",
        "spend_per_month": "NA",
        "time_period": "NA",
        "targeted_goal_amount": "NA",
        "monthly_investment_reqd": "NA"
    }


def getFinancialPlanningWithPriority(userId):
    data = fpQ5.objects.filter(created_by=userId)
    print("Fet financial goals.....")
    finacialGoals = {}
    finacialGoals["goals"] = ""
    finacialGoals["priority"] = ""
    if (data):
        goals = (data[0].goals).split("|")
        priority = (data[0].priority).split("|")

        for i in range(len(goals)):
            finacialGoals["goals"] += "<h1>" + \
                str(i+1) + ")" + goals[i] + "</h1>"
            # <div class="high">High</div>

            classMap = {
                "high": "high",
                "medium": "med",
                "low": "low",
                "1": "high",
                "2": "med",
                "3": "low"
            }

            g = """<div class='""" + classMap[priority[i]] + """'>""" + \
                priority[i].capitalize() + "</div>"
            finacialGoals["priority"] += g

    # <h1>1)₹40 Lakhs for Velijon's Graduation</h1>

    return finacialGoals


def familyFinancialGoals(userId):
    data = fpQ6.objects.filter(created_by=userId)
    res = ""

    # <h2>1) Be on track to achieve financial goals</h2>
    if data:
        goals = (data[0].goals).split("|")
        for ind, val in enumerate(goals, 1):
            res += "<h2>" + str(ind) + ")" + val+"</h2>"

    return res


def pdfGenerator(userId):
    print(f"userid is : {userId}")
    tz_IN = pytz.timezone('Asia/Kolkata')
    timecurr = datetime.now(tz_IN).strftime("%H:%M:%S")
    datecurr = datetime.today().date()

    cashflowBreakup(userId)
    assetAllocation(userId)
    financialInstrument(userId)
    networkHistory(userId)
    imgUrl = getImgUrl(userId)
    childMariage = getChildMarriageCalc(userId)
    emergencyFund = getEmergencyFundCalc(userId)
    childEdu = getChildEduCalc(userId)
    retirementCalc = getRetirementCalc(userId)
    visionFinancialPlanning = getFinancialPlanningWithPriority(userId)
    familyFinancialGoals1 = familyFinancialGoals(userId)

    print(visionFinancialPlanning)

    print(imgUrl)

    html = f"""
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"></script>
    
</head>
{CSS}
<body>
      <div class="container">


        <div class="mm">
            <div class="logo">
                <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/images/Logo+New.png" alt="" srcset="">
            </div>
            <div class="title">
                Financial Planning Report
            </div>
            <div class="date">
                {datecurr} || {timecurr}
            </div>

        <br />
        <br />

            <div class="m1">

                <div class="mmm">
                    <h1>Financial Goals of Family </h1>
                </div>


                <div class="lekha">

                {familyFinancialGoals1}
                </div>

            </div>
            <div class="m2">
                <img src="{imgUrl}" width="100%"
                    height="200" alt="">
            </div>

        </div>

        <br />
        <br />
        <br />
        <br />


        <div class="tu">


            <div class="tu1">
                <div class="tt">
                    <h1>Annual Cashflow Breakup</h1>
                </div>
                <img src="./{userId}c.png"
                    alt="" width="700" height="350">
            </div>

            <div class="tu2">
                <div class="tt">
                    <h1>Network History</h1>
                </div>
                <img src="./{userId}n.png"
                    alt="" width="700" height="300">
            </div>


        </div>

        <br />
        <br />

        <div class="same">


            <div class="sm">
                <div class="sm1">
                    <h1>Current Asset Allocation</h1>
                </div>
                <img src="./{userId}a.png" alt="" width="100%"
                    height="300" style="object-fit: contain;">
            </div>

            <div class="sma">
                <div class="sm2">
                    <h1>Financial Instruments Held</h1>
                </div>
                <img src="./{userId}f.png" alt="" width="460"
                    height="300" style="object-fit: contain;">
            </div>

        </div>

        <br />
        <br />
        <br />
        <br />
        <br />
        <br />

        <div class="same">


            <div class="sm">
                <div class="sm1">
                    <h1>Child Marriage Calculator</h1>
                </div>
                <div class="aaaab">
                    <h2>Estimated expenditure on child marriage (in Rs) : <b>{childMariage.spend}</b></h2>
                    <h2>Inflation Rate: <b>{childMariage.inflation_rate}</b> </h2>
                    <h2>Age (in years) : <b>{childMariage.age}</b></h2>
                    <h2>Expected Age of marriage of your child (in years) : <b>{childMariage.marriage_age}</b></h2>
                    <h2>Expected Investment Returns (in %) : <b>{childMariage.investment_return}</b></h2>
                    <h2>Full cost of your child marriage (in Rs)  : <b>{childMariage.child_marriage_cost}</b> </h2>
                    <h2>Monthly Investments Required (in Rs) : <b>{childMariage.monthly_investment}</b> </h2>
                </div>
            </div>

            <div class="sma">
                <div class="sm2">
                    <h1>Emergency Fund Calculator</h1>
                </div>
                <div class="aaaab">
                    <h2>Living Expenses (p.m.) (in Rs) :  <b>{emergencyFund.living_expense}</b> </h2>
                    <h2>Emergency Rate for (months) : <b>{emergencyFund.emergency_rate}</b>  </h2>
                    <h2>Emergency Fund to be accumulated in (months) (in Rs): <b>{emergencyFund.emergency_fund}</b> </h2>
                    <h2>Investment Return (p.a.) :  <b>{emergencyFund.investment_return}</b> </h2>
                    <h2>Emergency Fund you need (in Rs) :  <b>{emergencyFund.emergency_fund_you_need}</b> </h2>
                    <h2>Monthly investment Required (in Rs) :  <b>{emergencyFund.monthly_investment_reqd}</b>  </h2>

                </div>

            </div>

        </div>

        <div class="same">


            <div class="sm">
                <div class="sm1">
                    <h1>Child Education Calculator</h1>
                </div>
                <div class="aaaab">
                    <h2>Current cost of the desired education (in Rs) : <b>{childEdu.current_edu_cost}</b> </h2>
                    <h2>Inflation Rate : <b>{childEdu.inflation_rate}</b></h2>
                    <h2>Current Age of Child (in years) : <b>{childEdu.current_age}</b></h2>
                    <h2>Age of child when corpus is required (in years) : <b>{childEdu.corpus_age}</b></h2>
                    <h2>Expected Investment Returns (in %) : <b>{childEdu.expected_investment_return}</b> </h2>
                    <h2>Future cost of your child education (in Rs) : <b>{childEdu.future_edu_cost}</b></h2>
                    <h2>Monthly Investments required (in Rs) :  <b>{childEdu.monthly_investment_reqd}</b></h2>

                </div>

            </div>

            <div class="sma">
                <div class="sm2">
                    <h1>Retirement Calculator</h1>
                </div>
                <div class="aaaab">
                    <h2>Monthly Income Required In Retirement Years (in Rs) : <b>{retirementCalc.monthly_income_reqd}</b></h2>
                    <h2>Inflation Rate: <b>{retirementCalc.inflation_rate}</b> </h2>
                    <h2>Current Age (in years) : <b>{retirementCalc.age}</b> </h2>
                    <h2>Retirement Age (in years): <b>{retirementCalc.retirement_age}</b> </h2>
                    <h2>Life Espectancy (in years):: <b>{retirementCalc.life_expectancy}</b></h2>
                    <h2>Current Investment (in Rs) : <b>{retirementCalc.current_investment}</b></h2>
                    <h2>Rate of return: <b>{retirementCalc.return_rate}</b></h2>
                    <h2>Annual Income Required Immediately After Retirement (in Rs) : <b>{retirementCalc.annual_income_retire}</b></h2>
                    <h2>Appreciation of investment made today (in Rs) : <b>{retirementCalc.investment_appreciation}</b></h2>
                </div>

            </div>

        </div>

        <div class="same">


                <div class="sm">
                    <div class="sm1">
                        <h1> Family's Vision of Financial Welbeing</h1>
                    </div>
    
                    <div class="part">
                        <div class="pp1">
                        <h1 style="font-size: 17px; padding-top:12px;">Goal</h1>
                            {visionFinancialPlanning['goals']}
    
                        </div>
                        <div class="pp2">
                            <h1>Priority</h1>
                            <div class="imm">
                                <!-- <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/financialpdf/images/level.png"
                                    alt=""> -->
    
                             {visionFinancialPlanning['priority']}

                                    
                            </div>
    
    
    
                        </div>
    
    
                    </div>


            </div>

            <div class="sma">


                <div class="sm2">


                    <h1>Actions Recommended(Short-term)</h1>


                </div>
                <div class="mmi">
                    <h1>Kindly get in touch with our financial advisor to start your wealth & Financial freedom journey</h1>
                </div>
            </div>
        </div>

        <div class="end">

        </div>
    </div>
    </div>


</body>


</html>
    """
    userId = userId
    file_name = os.path.join("finantialPlanning", f"{userId}.html")
    text_file = open(file_name, "w", encoding="utf-8")
    text_file.write(html)
    text_file.close()

    filename = os.path.join("finantialPlanning", f"{userId}.pdf")
    options = {
        "enable-local-file-access": None
    }
    try:
        pdfkit.from_file(file_name, filename, options=options)
    except Exception as e:
        return 1
    return 1


def delete_files(userId):
    cwd = os.path.dirname(__file__)
    os.remove(f"{cwd}/{userId}.html")
    os.remove(f"{cwd}/{userId}.pdf")
    os.remove(f"{cwd}/{userId}a.png")
    os.remove(f"{cwd}/{userId}c.png")
    os.remove(f"{cwd}/{userId}f.png")
    os.remove(f"{cwd}/{userId}n.png")

    print("files deleted")


def uploadFile(userId):
    user = userId
    filename = os.path.join("finantialPlanning", f"{user}.pdf")
    uploadFileName = f"financialpdf/{userId}.pdf"
    fullFilePath = "https://stocktick1.s3.ap-south-1.amazonaws.com/" + uploadFileName
    try:
        s3.upload_file(filename, 'stocktick1', uploadFileName)
        try:
            delete_files(userId)
            pass
        except Exception as e:
            print(e)
        return fullFilePath
    except Exception as e:  # getting permission denied error
        print(e)
        return False
