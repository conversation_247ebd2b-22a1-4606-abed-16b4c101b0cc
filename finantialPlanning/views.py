from importlib.metadata import PathDistribution
from time import timezone
from django.shortcuts import render
from rest_framework.views import APIView
from .models import *
from .serializer import *
from rest_framework.response import Response
from rest_framework import status
from .utils import *
# from .utils import slugGenerator, getCategories, KeyCatChange
from users.constant import ACTIVE
from users.auth import verifyUser
from rest_framework.permissions import IsAdminUser
import datetime
import pytz
# Create your views here.
IST = pytz.timezone('Asia/Kolkata')


class IsSuperUser(IsAdminUser):
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_superuser)


class FinnantialQuestion1(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = fpQ1.objects.filter(created_by=userId)
            user = fpQ1Serializer(user, many=True)
            return Response(user.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            user_Q1 = fpQ1.objects.filter(created_by=userId)
            if (user_Q1):
                try:
                    lstatus = data["status"]
                except:
                    lstatus = user_Q1[0].status

                try:
                    children = data["children"]
                except:
                    children = user_Q1[0].children

                try:
                    parent = data["parents"]
                except:
                    parent = user_Q1[0].parents

                k = fpQ1.objects.filter(created_by=userId).update(
                    lstatus=lstatus, children=children, parents=parent, modified_by=userId, modified_at=datetime.datetime.now())
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                # now = timezone.now()
                # print(now)
                user_Q1 = fpQ1.objects.create(
                    created_by=userId,
                    lstatus=data["status"],
                    children=data["children"],
                    parents=data["parents"],
                )
                user_Q1.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class FinnantialQuestion2(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            print("userId", userId)
            data = fpQ2.objects.filter(created_by=userId)
            print(data)
            data = fpQ2Serializer(data, many=True)
            return Response(data.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        # print(data)
        try:
            user_Q2 = fpQ2.objects.filter(created_by=userId)
            print(user_Q2)
            if (user_Q2):
                try:
                    invst_amount = data["invst_amount"]
                except:
                    invst_amount = user_Q2[0].invst_amount

                try:
                    household_expns = data["household_expns"]
                except:
                    household_expns = user_Q2[0].household_expns

                try:
                    lifestyle_expns = data["lifestyle_expns"]
                except:
                    lifestyle_expns = user_Q2[0].lifestyle_expns

                try:
                    surplus = data["surplus"]
                except:
                    surplus = user_Q2[0].surplus

                try:
                    tax_paid = data["tax_paid"]
                except:
                    tax_paid = user_Q2[0].tax_paid

                try:
                    emi_paid = data["emi_paid"]
                except:
                    emi_paid = user_Q2[0].emi_paid

                k = fpQ2.objects.filter(created_by=userId).update(invst_amount=invst_amount,
                                                                  household_expns=household_expns,
                                                                  surplus=surplus,
                                                                  lifestyle_expns=lifestyle_expns,
                                                                  tax_paid=tax_paid,
                                                                  emi_paid=emi_paid,
                                                                  modified_by=userId)
                print("modified")
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                now = datetime.datetime.now()
                print(now)
                user_Q2 = fpQ2.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    invst_amount=data["invst_amount"],
                    household_expns=data["household_expns"],
                    lifestyle_expns=data["lifestyle_expns"],
                    surplus=data["surplus"],
                    tax_paid=data["tax_paid"],
                    emi_paid=data["emi_paid"],
                )
                print("saved")
                user_Q2.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_Q3(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            print(userId)
            data = fpQ3.objects.filter(created_by=userId)
            print(data[0].gold)
            D = {
                "gold": data[0].gold,
                "equity": data[0].equity,
                "estate": data[0].estate,
                "debt": data[0].debt,
            }
            print(D)
            return Response(D, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        # print(data)
        try:
            user_Q3 = fpQ3.objects.filter(created_by=userId)
            print(user_Q3)
            if (user_Q3):

                try:
                    gold = data["gold"]
                except:
                    gold = user_Q3[0].gold

                try:
                    equity = data["equity"]
                except:
                    equity = user_Q3[0].equity

                try:
                    debt = data["debt"]
                except:
                    debt = user_Q3[0].debt

                try:
                    estate = data["estate"]
                except:
                    estate = user_Q3[0].estate

                fpQ3.objects.filter(created_by=userId).update(
                    gold=gold, equity=equity, debt=debt, estate=estate, modified_by=userId, modified_at=datetime.datetime.now())
                print("modified")
                return Response({"message": "Data updated"}, status=status.HTTP_201_CREATED)

            else:
                now = datetime.datetime.now()
                print(now)
                user_Q3 = fpQ3.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    gold=data["gold"],
                    equity=data["equity"],
                    estate=data["estate"],
                    debt=data["debt"],
                )
                print("saved")
                user_Q3.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_Q4(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            data = fpQ4.objects.filter(created_by=userId)
            data = {
                "md": data[0].md,
                "mf": data[0].mf,
                "nps": data[0].nps,
                "shares": data[0].shares,
            }
            return Response(data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        # print(data)
        try:
            user_Q4 = fpQ4.objects.filter(created_by=userId)
            if (user_Q4):

                try:
                    md = data["md"]
                except:
                    md = user_Q4[0].md

                try:
                    mf = data["mf"]
                except:
                    mf = user_Q4[0].mf

                try:
                    nps = data["nps"]
                except:
                    nps = user_Q4[0].nps

                try:
                    shares = data["shares"]
                except:
                    shares = user_Q4[0].shares

                fpQ4.objects.filter(created_by=userId).update(
                    md=md, mf=mf, nps=nps, shares=shares, modified_by=userId, modified_at=datetime.datetime.now())
                print("modified")
                return Response({"message": "Data updated"}, status=status.HTTP_201_CREATED)
            else:
                now = datetime.datetime.now()
                print(now)
                user_Q4 = fpQ4.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    md=data["md"],
                    mf=data["mf"],
                    nps=data["nps"],
                    shares=data["shares"],
                )
                print("saved")
                user_Q4.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_Q5(APIView):
    def get(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user_Q5 = fpQ5.objects.filter(created_by=userId)
            if (user_Q5):
                data = []
                goals = user_Q5[0].goals.split('|')
                priority = user_Q5[0].priority.split('|')

                for i in range(len(goals)):
                    data.append({
                        "goal": goals[i],
                        "priority": priority[i]
                    })

                return Response(data, status=status.HTTP_201_CREATED)
            else:
                return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)
        except:
            return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            user_Q5 = fpQ5.objects.filter(created_by=userId)
            if (user_Q5):
                now = datetime.datetime.now()
                print(now)
                data = data["data"]
                x = "".join(f"{i['goal']}|" for i in data)
                y = "".join(f"{i['priority']}|" for i in data)
                x = x[:-1]
                y = y[:-1]
                fpQ5.objects.filter(created_by=userId).update(
                    goals=x, priority=y, modified_by=userId, modified_at=datetime.datetime.now())
                return Response({"message": "Data updated"}, status=status.HTTP_201_CREATED)
            else:
                now = datetime.datetime.now()
                print(now)
                data = data["data"]
                x = "".join(f"{i['goal']}|" for i in data)
                y = "".join(f"{i['priority']}|" for i in data)
                x = x[:-1]
                y = y[:-1]
                user_Q5 = fpQ5.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    goals=x,
                    priority=y
                )
                # print("saved")
                user_Q5.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)

# ------------------------------------Question 6 ------------------------


class finantialPlanning_Q6(APIView):
    def get(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user_Q6 = fpQ6.objects.filter(created_by=userId)
            if (user_Q6):
                goals = user_Q6[0].goals.split('|')
                return Response(goals, status=status.HTTP_201_CREATED)
            else:
                return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)
        except:
            return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            user_Q6 = fpQ6.objects.filter(created_by=userId)
            if (user_Q6):
                now = datetime.datetime.now()
                print(now)
                goals = data["goals"]
                G = ''.join(f"{i}|" for i in goals)
                G = G[:-1]
                fpQ6.objects.filter(created_by=userId).update(
                    modified_by=userId, modified_at=datetime.datetime.now(), goals=G)
                return Response({"message": "Data updated"}, status=status.HTTP_201_CREATED)
            else:
                now = datetime.datetime.now()
                print(now)
                goals = data["goals"]
                G = ''.join(f"{i}|" for i in goals)
                G = G[:-1]

                user_Q6 = fpQ6.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    goals=G
                )
                # print("saved")
                user_Q6.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Server Error"}, status=status.HTTP_404_NOT_FOUND)


# ---------------------------------- Q7 ------------------ [ takes a img url and stores it ]

class finantialPlanning_Q7(APIView):
    def get(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        user_Q7 = fpQ7.objects.filter(created_by=userId)
        if (user_Q7):
            return Response({"img": user_Q7[0].img}, status=status.HTTP_201_CREATED)
        else:
            return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)

        try:
            user_Q7 = fpQ7.objects.filter(created_by=userId)
            if (user_Q7):
                now = datetime.datetime.now()
                fpQ7.objects.filter(created_by=userId).update(
                    modified_by=userId, modified_at=datetime.datetime.now(), img=data['img'])
                return Response({"message": "Data updated"}, status=status.HTTP_201_CREATED)
            else:
                now = datetime.datetime.now()
                print(now)
                user_Q7 = fpQ7.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    img=data['img']
                )
                # print("saved")
                user_Q7.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

# ----------------------------------------------------$$ -----------------------------------


class finantialPlanning_Q8(APIView):
    def get(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        user_Q8 = fpQ8.objects.filter(created_by=userId)
        if (user_Q8):
            years = user_Q8[0].years.split('|')
            assets = user_Q8[0].assets.split('|')
            liability = user_Q8[0].liability.split('|')
            net_worth = user_Q8[0].net_worth.split('|')
            data = []
            for i in range(len(years)):
                data.append({
                    "year": years[i],
                    "asset": assets[i],
                    "liability": liability[i],
                    "net_worth": net_worth[i],
                })
            return Response({"data": data}, status=status.HTTP_201_CREATED)
        else:
            return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        # print(data)

        try:
            user_Q8 = fpQ8.objects.filter(created_by=userId)
            if (user_Q8):
                now = datetime.datetime.now()
                # print(now)
                data = data['data']
                year = ''.join(f"{i['year']}|" for i in data)
                year = year[:-1]
                asset = ''.join(f"{i['asset']}|" for i in data)
                asset = asset[:-1]
                liability = ''.join(f"{i['liability']}|" for i in data)
                liability = liability[:-1]
                networth = ''.join(f"{i['networth']}|" for i in data)
                networth = networth[:-1]

                fpQ8.objects.filter(created_by=userId).update(
                    modified_by=userId,
                    modified_at=datetime.datetime.now(),
                    years=year,
                    assets=asset,
                    liability=liability,
                    net_worth=networth
                )
                return Response({"message": "Data updated"}, status=status.HTTP_201_CREATED)
            else:
                now = datetime.datetime.now()
                # print(now)
                data = data['data']
                year = ''.join(f"{i['year']}|" for i in data)
                year = year[:-1]
                asset = ''.join(f"{i['asset']}|" for i in data)
                asset = asset[:-1]
                liability = ''.join(f"{i['liability']}|" for i in data)
                liability = liability[:-1]
                networth = ''.join(f"{i['networth']}|" for i in data)
                networth = networth[:-1]

                user_Q8 = fpQ8.objects.create(
                    created_by=userId,
                    created_at=now,
                    modified_at=now,
                    years=year,
                    assets=asset,
                    liability=liability,
                    net_worth=networth
                )
                user_Q8.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not found"}, status=status.HTTP_404_NOT_FOUND)


class ReportPdf(APIView):
    def get(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        if not token:
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        flg = pdfGenerator(userId)
        k = False
        k = uploadFile(userId)
        print(k)
        if flg == 1 and k:
            return Response({"message": "Report Generated and Uploaded", "url": k}, status=status.HTTP_201_CREATED)
        elif flg == 1 and not k:
            return Response({"message": "Report Generated"}, status=status.HTTP_201_CREATED)
        else:
            return Response({"message": "Report Not Generated"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_ChildMarriage(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = fpChildMarriageCalc.objects.filter(created_by=userId)
            user = fpChildMarriageCalcSerializer(user, many=True)
            return Response(user.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            res = fpChildMarriageCalc.objects.filter(created_by=userId)
            if (res):
                try:
                    spend = data["spend"]
                except:
                    spend = res[0].spend

                try:
                    inflation_rate = data["inflation_rate"]
                except:
                    inflation_rate = res[0].inflation_rate

                try:
                    age = data["age"]
                except:
                    age = res[0].age

                try:
                    marriage_age = data["marriage_age"]
                except:
                    marriage_age = res[0].marriage_age

                try:
                    investment_return = data["investment_return"]
                except:
                    investment_return = res[0].investment_return

                try:
                    child_marriage_cost = data["child_marriage_cost"]
                except:
                    child_marriage_cost = res[0].child_marriage_cost

                try:
                    monthly_investment = data["monthly_investment"]
                except:
                    monthly_investment = res[0].monthly_investment

                # k = fpQ1.objects.filter(created_by=userId).update(
                #     lstatus=lstatus, children=children, parents=parent, modified_by=userId, modified_at=datetime.datetime.now())

                k = fpChildMarriageCalc.objects.filter(created_by=userId).update(
                    spend=spend, inflation_rate=inflation_rate, age=age, marriage_age=marriage_age, investment_return=investment_return, child_marriage_cost=child_marriage_cost, monthly_investment=monthly_investment
                )
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                # now = timezone.now()
                # print(now)
                resr = fpChildMarriageCalc.objects.create(
                    created_by=userId,
                    modified_by=userId,
                    spend=data["spend"],
                    inflation_rate=data["inflation_rate"],
                    age=data["age"],
                    marriage_age=data["marriage_age"],
                    investment_return=data["investment_return"],
                    child_marriage_cost=data["child_marriage_cost"],
                    monthly_investment=data["monthly_investment"]
                )
                resr.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_EmergencyFundCalc(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = fpEmergencyFundCalc.objects.filter(created_by=userId)
            user = fpEmergencyFundCalcSerializer(user, many=True)
            return Response(user.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            res = fpEmergencyFundCalc.objects.filter(created_by=userId)
            if (res):
                try:
                    living_expense = data["living_expense"]
                except:
                    living_expense = res[0].living_expense

                try:
                    emergency_rate = data["emergency_rate"]
                except:
                    emergency_rate = res[0].emergency_rate

                try:
                    emergency_fund = data["emergency_fund"]
                except:
                    emergency_fund = res[0].emergency_fund

                try:
                    investment_return = data["investment_return"]
                except:
                    investment_return = res[0].investment_return

                try:
                    emergency_fund_you_need = data["emergency_fund_you_need"]
                except:
                    emergency_fund_you_need = res[0].emergency_fund_you_need

                try:
                    monthly_investment_reqd = data["monthly_investment_reqd"]
                except:
                    monthly_investment_reqd = res[0].monthly_investment_reqd

                # k = fpQ1.objects.filter(created_by=userId).update(
                #     lstatus=lstatus, children=children, parents=parent, modified_by=userId, modified_at=datetime.datetime.now())

                k = fpEmergencyFundCalc.objects.filter(created_by=userId).update(
                    living_expense=living_expense, emergency_rate=emergency_rate, emergency_fund=emergency_fund, emergency_fund_you_need=emergency_fund_you_need, investment_return=investment_return, monthly_investment_reqd=monthly_investment_reqd
                )
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                # now = timezone.now()
                # print(now)
                resr = fpEmergencyFundCalc.objects.create(
                    created_by=userId,
                    modified_by=userId,
                    living_expense=data["living_expense"],
                    emergency_rate=data["emergency_rate"],
                    emergency_fund=data["emergency_fund"],
                    investment_return=data["investment_return"],
                    emergency_fund_you_need=data["emergency_fund_you_need"],
                    monthly_investment_reqd=data["monthly_investment_reqd"]
                )
                resr.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_EmergencyFundCalc(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = fpEmergencyFundCalc.objects.filter(created_by=userId)
            user = fpEmergencyFundCalcSerializer(user, many=True)
            return Response(user.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            res = fpEmergencyFundCalc.objects.filter(created_by=userId)
            if (res):
                try:
                    living_expense = data["living_expense"]
                except:
                    living_expense = res[0].living_expense

                try:
                    emergency_rate = data["emergency_rate"]
                except:
                    emergency_rate = res[0].emergency_rate

                try:
                    emergency_fund = data["emergency_fund"]
                except:
                    emergency_fund = res[0].emergency_fund

                try:
                    investment_return = data["investment_return"]
                except:
                    investment_return = res[0].investment_return

                try:
                    emergency_fund_you_need = data["emergency_fund_you_need"]
                except:
                    emergency_fund_you_need = res[0].emergency_fund_you_need

                try:
                    monthly_investment_reqd = data["monthly_investment_reqd"]
                except:
                    monthly_investment_reqd = res[0].monthly_investment_reqd

                # k = fpQ1.objects.filter(created_by=userId).update(
                #     lstatus=lstatus, children=children, parents=parent, modified_by=userId, modified_at=datetime.datetime.now())

                k = fpEmergencyFundCalc.objects.filter(created_by=userId).update(
                    living_expense=living_expense, emergency_rate=emergency_rate, emergency_fund=emergency_fund, emergency_fund_you_need=emergency_fund_you_need, investment_return=investment_return, monthly_investment_reqd=monthly_investment_reqd
                )
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                # now = timezone.now()
                # print(now)
                resr = fpEmergencyFundCalc.objects.create(
                    created_by=userId,
                    modified_by=userId,
                    living_expense=data["living_expense"],
                    emergency_rate=data["emergency_rate"],
                    emergency_fund=data["emergency_fund"],
                    investment_return=data["investment_return"],
                    emergency_fund_you_need=data["emergency_fund_you_need"],
                    monthly_investment_reqd=data["monthly_investment_reqd"]
                )
                resr.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_ChildEduCalc(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = fpChildEduCalc.objects.filter(created_by=userId)
            user = fpChildEduCalcSerializer(user, many=True)
            return Response(user.data, status=status.HTTP_201_CREATED)
        except:
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            res = fpChildEduCalc.objects.filter(created_by=userId)
            if (res):
                try:
                    current_edu_cost = data["current_edu_cost"]
                except:
                    current_edu_cost = res[0].current_edu_cost

                try:
                    inflation_rate = data["inflation_rate"]
                except:
                    inflation_rate = res[0].inflation_rate

                try:
                    current_age = data["current_age"]
                except:
                    current_age = res[0].current_age

                try:
                    corpus_age = data["corpus_age"]
                except:
                    corpus_age = res[0].corpus_age

                try:
                    expected_investment_return = data["expected_investment_return"]
                except:
                    expected_investment_return = res[0].expected_investment_return

                try:
                    future_edu_cost = data["future_edu_cost"]
                except:
                    future_edu_cost = res[0].future_edu_cost

                try:
                    monthly_investment_reqd = data["monthly_investment_reqd"]
                except:
                    monthly_investment_reqd = res[0].monthly_investment_reqd

                # k = fpQ1.objects.filter(created_by=userId).update(
                #     lstatus=lstatus, children=children, parents=parent, modified_by=userId, modified_at=datetime.datetime.now())

                k = fpChildEduCalc.objects.filter(created_by=userId).update(
                    current_edu_cost=current_edu_cost, inflation_rate=inflation_rate, current_age=current_age, corpus_age=corpus_age, expected_investment_return=expected_investment_return, monthly_investment_reqd=monthly_investment_reqd, future_edu_cost=future_edu_cost
                )
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                # now = timezone.now()
                # print(now)
                resr = fpChildEduCalc.objects.create(
                    created_by=userId,
                    modified_by=userId,
                    current_edu_cost=data["current_edu_cost"],
                    inflation_rate=data["inflation_rate"],
                    current_age=data["current_age"],
                    corpus_age=data["corpus_age"],
                    expected_investment_return=data["expected_investment_return"],
                    future_edu_cost=data["future_edu_cost"],
                    monthly_investment_reqd=data["monthly_investment_reqd"]
                )
                resr.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)


class finantialPlanning_RetirementCalc(APIView):
    def get(self, request):
        print("request recived")
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
                print(userId)
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = fpRetirementCalc.objects.filter(created_by=userId)
            user = fpRetirementCalcSerializer(user, many=True)
            return Response(user.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"msg": "Data not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        try:
            token = request.headers["authToken"]
        except:
            try:
                token = request.query_params['authToken']
            except:
                return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)
        try:
            resp = verifyUser(token)
            if resp["message"]:
                userId = resp["id"]
            else:
                return Response({"message": "Invalid Auth Token"}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e.args[0])
            return Response({"message": "AuthToken Not Found"}, status=status.HTTP_404_NOT_FOUND)

        data = request.data
        print(data)
        try:
            res = fpRetirementCalc.objects.filter(created_by=userId)
            if (res):
                try:
                    monthly_income_reqd = data["monthly_income_reqd"]
                except:
                    monthly_income_reqd = res[0].monthly_income_reqd

                try:
                    inflation_rate = data["inflation_rate"]
                except:
                    inflation_rate = res[0].inflation_rate

                try:
                    age = data["age"]
                except:
                    age = res[0].age

                try:
                    retirement_age = data["retirement_age"]
                except:
                    retirement_age = res[0].retirement_age

                try:
                    life_expectancy = data["life_expectancy"]
                except:
                    life_expectancy = res[0].life_expectancy

                try:
                    current_investment = data["current_investment"]
                except:
                    current_investment = res[0].current_investment

                try:
                    return_rate = data["return_rate"]
                except:
                    return_rate = res[0].return_rate

                try:
                    annual_income_retire = data["annual_income_retire"]
                except:
                    annual_income_retire = res[0].annual_income_retire

                try:
                    investment_appreciation = data["investment_appreciation"]
                except:
                    investment_appreciation = res[0].investment_appreciation

                try:
                    corpus_deficit = data["corpus_deficit"]
                except:
                    corpus_deficit = res[0].corpus_deficit
                try:
                    lumpsum_fund_reqd = data["lumpsum_fund_reqd"]
                except:
                    lumpsum_fund_reqd = res[0].lumpsum_fund_reqd
                try:
                    monthly_investment_reqd = data["monthly_investment_reqd"]
                except:
                    monthly_investment_reqd = res[0].monthly_investment_reqd

                # k = fpQ1.objects.filter(created_by=userId).update(
                #     lstatus=lstatus, children=children, parents=parent, modified_by=userId, modified_at=datetime.datetime.now())

                k = fpRetirementCalc.objects.filter(created_by=userId).update(
                    monthly_income_reqd=monthly_income_reqd,  inflation_rate=inflation_rate, age=age, retirement_age=retirement_age,
                    life_expectancy=life_expectancy, current_investment=current_investment, return_rate=return_rate,
                    annual_income_retire=annual_income_retire, investment_appreciation=investment_appreciation, corpus_deficit=corpus_deficit,
                    lumpsum_fund_reqd=lumpsum_fund_reqd, monthly_investment_reqd=monthly_investment_reqd
                )

                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
            else:
                # now = timezone.now()
                # print(now)
                resr = fpRetirementCalc.objects.create(
                    created_by=userId,
                    modified_by=userId,
                    monthly_income_reqd=data["monthly_income_reqd"],
                    inflation_rate=data["inflation_rate"],
                    age=data["age"],
                    life_expectancy=data["life_expectancy"],
                    current_investment=data["current_investment"],
                    return_rate=data["return_rate"],
                    annual_income_retire=data["annual_income_retire"],
                    investment_appreciation=data["investment_appreciation"],
                    corpus_deficit=data["corpus_deficit"],
                    lumpsum_fund_reqd=data["lumpsum_fund_reqd"],
                    monthly_investment_reqd=data["monthly_investment_reqd"],
                    retirement_age=data["retirement_age"]
                )

                resr.save()
                return Response({"message": "Data saved"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            print(e)
            return Response({"message": "Data not saved"}, status=status.HTTP_404_NOT_FOUND)
