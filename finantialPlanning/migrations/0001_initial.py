# Generated by Django 4.0.2 on 2022-03-28 15:17

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='fpQ1',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('lstatus', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, default='NA', max_length=10, null=True)),
                ('children', models.IntegerField()),
                ('parents', models.Char<PERSON>ield(blank=True, max_length=45, null=True)),
            ],
            options={
                'db_table': 'finantialPlanning_Q1',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ2',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('invst_amount', models.IntegerField()),
                ('household_expns', models.IntegerField()),
                ('lifestyle_expns', models.IntegerField()),
                ('surplus', models.IntegerField()),
                ('tax_paid', models.IntegerField()),
                ('emi_paid', models.IntegerField()),
            ],
            options={
                'db_table': 'finantialPlanning_Q2',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ3',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('gold', models.IntegerField(blank=True, null=True)),
                ('equity', models.IntegerField(blank=True, null=True)),
                ('estate', models.IntegerField(blank=True, null=True)),
                ('debt', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'finantialPlanning_Q3',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ4',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('md', models.IntegerField()),
                ('mf', models.IntegerField()),
                ('nps', models.IntegerField()),
                ('shares', models.IntegerField()),
            ],
            options={
                'db_table': 'finantialPlanning_Q4',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ5',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('goals', models.CharField(blank=True, max_length=200, null=True)),
                ('priority', models.CharField(blank=True, max_length=200, null=True)),
            ],
            options={
                'db_table': 'finantialPlanning_Q5',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ6',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('goals', models.CharField(max_length=200)),
            ],
            options={
                'db_table': 'finantialPlanning_Q6',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ7',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'ACTIVE'), ('INACTIVE', 'INACTIVE'), ('DELETED', 'DELETED')], default='ACTIVE', max_length=10)),
                ('img', models.CharField(max_length=200)),
            ],
            options={
                'db_table': 'finantialPlanning_Q7',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='fpQ8',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(blank=True, max_length=45, null=True)),
                ('created_at', models.DateTimeField(auto_now=True, null=True)),
                ('modified_by', models.CharField(blank=True, max_length=45, null=True)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('year', models.CharField(blank=True, max_length=45, null=True)),
                ('assets', models.IntegerField(blank=True, null=True)),
                ('liability', models.IntegerField(blank=True, null=True)),
                ('net_worth', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'finantialPlanning_Q8',
                'managed': False,
            },
        ),
    ]
