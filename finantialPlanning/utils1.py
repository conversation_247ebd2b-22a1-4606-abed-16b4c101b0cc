import matplotlib.pyplot as plt
import os
from cProfile import label
from operator import ne
from datetime import date, datetime
import pytz
import random
# from tkinter import CENTER
# from turtle import color
from uuid import uuid4

from .models import *
from .serializer import *

from users.constant import ACTIVE
from .css import CSS
import pdfkit
import boto3
from django.conf import settings
import matplotlib
matplotlib.use('Agg')

s3 = boto3.client(
    's3',
    aws_access_key_id=settings.ACCESS_KEY,
    aws_secret_access_key=settings.SECRET_ACCESS_KEY,
)


def cashflowBreakup(userId):
    plt.clf()
    total = fpQ2.objects.filter(created_by=userId)
    s = fpQ2Serializer(total, many=True)
    s = s.data[0]
    lbl = ['Investment Amount', 'Household Expense',
           'Lifestyle Expense', 'Surplus', 'Tax Paid', 'EMI Paid']
    data = [s['invst_amount'], s['household_expns'],
            s['lifestyle_expns'], s['surplus'], s['tax_paid'], s['emi_paid']]
    plt.pie(data, labels=lbl, autopct="%1.1f%%", radius=1.5,
            wedgeprops={'linewidth': 3.0, 'edgecolor': 'white'})
    plt.axis('equal')

    try:
        plt.savefig(f"finantialPlanning/{userId}c.png")
        return f"./{userId}c.png"
    except Exception as e:
        return 0


def assetAllocation(userId):
    total = fpQ3.objects.filter(created_by=userId)
    s = fpQ3Serializer(total, many=True)
    s = s.data[0]
    plt.clf()
    lbl = ['Gold', 'Equity', 'Real Estate', 'Debt']

    data = [s['gold'], s['equity'], s['estate'], s['debt']]
    plt.figure(figsize=(10, 10))
    plt.pie(data, labels=lbl, autopct="%1.1f%%", pctdistance=0.75, radius=1.25, wedgeprops={'linewidth': 3.0, 'edgecolor': 'white'},
            textprops={'size': 'xx-large'})
    centre_circle = plt.Circle((0, 0), 0.70, fc='white')
    fig = plt.gcf()

    fig.gca().add_artist(centre_circle)
    plt.axis('equal')
    try:
        plt.savefig(f"finantialPlanning/{userId}a.png")
        return f"../{userId}a.png"
    except Exception as e:
        return 0


def financialInstrument(userId):
    total = fpQ4.objects.filter(created_by=userId)
    s = fpQ4Serializer(total, many=True)
    s = s.data[0]
    plt.clf()
    lbl = ['Fixed Deposits', 'Mutual Funds', 'EPF/PPF/NSP', 'Shares']

    data = [s['md'], s['mf'], s['nps'], s['shares']]

    fig, ax = plt.subplots(figsize=(15, 5))
    bars = ax.barh(lbl, data, height=0.5, color='orange')
    for i, v in enumerate(data):
        ax.text(v + 3, i, str(v),
                color='black', fontweight='bold',)

    # Hide axes ticks
    ax.set_xticks([])
    plt.yticks(rotation=17)

    try:
        plt.savefig(f"finantialPlanning/{userId}f.png")
        return f"./{userId}f.png"
    except Exception as e:
        return 0


def networkHistory(userId):
    total = fpQ8.objects.filter(created_by=userId)
    s = fpQ8Serializer(total, many=True)
    s = s.data[0]
    plt.clf()
    asset = s['assets'].split('|')
    asset = list(map(int, asset))
    liability = s['liability'].split('|')
    liability = list(map(int, liability))
    networth = s['net_worth'].split('|')
    networth = list(map(int, networth))
    year = s['years'].split('|')
    plt.plot(year, liability, color='orange', linewidth=3)
    plt.plot(year, asset, linewidth=3)

    plt.plot(year, networth, color='grey', linewidth=3)

    leg = plt.legend(['liability', 'asset', 'networth'])

    leg_lines = leg.get_lines()
    leg_texts = leg.get_texts()
    # bulk-set the properties of all lines and texts
    plt.setp(leg_lines, linewidth=4)
    plt.setp(leg_texts, fontsize='x-large')

    try:
        plt.savefig(f"finantialPlanning/{userId}n.png")
        return f"./{userId}n.png"
    except Exception as e:
        return 0


def pdfGenerator(userId):
    print(f"userid is : {userId}")
    tz_IN = pytz.timezone('Asia/Kolkata')
    timecurr = datetime.now(tz_IN).strftime("%H:%M:%S")
    datecurr = datetime.today().date()
    
    cashflowBreakup(userId)
    assetAllocation(userId)
    financialInstrument(userId)
    networkHistory(userId)

    html = f"""
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"></script>
    
</head>
{CSS}
<body>
     
    <div class="container">

        <div class="mm">
        <div class="logo">
            <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/images/Logo+New.png" alt="" srcset="">
        </div>
        <div class="title">
            Financial Planning Report
        </div>
        <div class="date">
            {datecurr} || {timecurr}
        </div>

            <br />
            <br />

            <div class="m1">

                <div class="mmm">
                    <h1> Family's Vision of Financial Welbeing</h1>
                </div>

                <div class="lekha">

                    <h2>1) Be on track to achieve financial goals</h2>
                    <h2>2) Become debt free. pay off home loan</h2>
                    <h2>3) Get adequately insured for all financial risks</h2>
                    <h2>4) Create a comfortable retirement corpus</h2>
                    <h2>5) Have peace of mind around money matters</h2>
                </div>

            </div>
            <div class="m2">
                <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/financialpdf/images/family.png" width="100%" height="250" alt="">
            </div>

        </div>

        <br />
        <br />

        <div class="tu">


            <div class="tu1">
                <div class="tt">
                    <h1>Annual Cashflow Breakup</h1>
                </div>
                <img src="./{userId}c.png" alt="" width="100%" height="250">
            </div>

            <div class="tu2">
                <div class="tt">
                    <h1>Network History</h1>
                </div>
                <img src="./{userId}n.png" alt="" width="100%" height="250">
            </div>


        </div>

        <br />
        <br />



        <div class="same">


            <div class="sm">
                <div class="sm1">
                    <h1>Current Asset Allocation</h1>
                </div>
                <img src="./{userId}a.png" alt="" width="100%" height="250">
            </div>

            <div class="sma">
                <div class="sm2">
                    <h1>Financial Instruments Held</h1>
                </div>
                <img src="./{userId}f.png" alt="" width="100%" height="250">
            </div>
        </div>

        <br />
        <br />
        <br />
        <br />

        <div class="same">


            <div class="sm">
                <div class="sm1">
                    <h1>Financial Goals of Family</h1>
                </div>

                <div class="part">
                    <div class="pp1">
                        <h1 style="font-size: 17px; padding-top:12px;">Goal</h1>
                        <h1>1)₹40 Lakhs for Velijon's Graduation</h1>
                        <h1>2)₹10 Lakhs for Car Upgrade</h1>
                        <h1>3)₹3 Crores for Retirement Corpus</h1>
                        <h1>4)₹40 Lakhs for Venessa Graduation</h1>
                        <h1 style="font-size: 15px;">5)₹60 Lakhs for 2nd House Downpayment</h1>

                    </div>
                    <div class="pp2">
                        <h1>Priority</h1>
                        <div class="imm">
                            <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/financialpdf/images/level.png"
                                alt="">
                        </div>

                       

                    </div>


                </div>


            </div>

            <div class="sma">


                <div class="sm2">


                    <h1>Actions Recommended(Short-term)</h1>


                </div>
                <div class="mmi">
                    <h1>1.Buy Term Life Insurance Cover of ₹2 Crores for self</h1>
                    <h1>2.Start Sip of ₹30,000 in recommended Shemes</h1>
                    <h1>3.Organize & consolidate all financial documents</h1>
                    <h1>4.Buy Critical Insurance Cover of ₹50 Lakhs for self</h1>
                    <h1>5.Buy Critical Insurance Cover of ₹50 Lakhs for self</h1>
                </div>
            </div>
        </div>

        <div class="end">

            <div class="em">
                <h1>Prepared By Rahul Agarwal,QPEP for Philip Naronha Family on February 16th, 2022</h1>
            </div>

            <h1 style="font-size: 15px ; padding-left:10px; padding-top:0px;">&copy; Network FP knowledge Solution
                Pvt.Ltd,NFP ProMembers are only licensed to use Protool</h1>

        </div>


    </div>


    </div>






</body>


</html>
    """
    userId = userId
    file_name = os.path.join("finantialPlanning",f"{userId}.html")
    text_file = open(file_name, "w", encoding="utf-8")
    text_file.write(html)
    text_file.close()

    filename = os.path.join("finantialPlanning",f"{userId}.pdf")
    options = {
        "enable-local-file-access": None
    }
    try:
        pdfkit.from_file(file_name, filename, options=options)
    except Exception as e:
        return 1
    return 1


def delete_files(userId):
    cwd = os.path.dirname(__file__)
    os.remove(f"{cwd}/{userId}.html")
    os.remove(f"{cwd}/{userId}.pdf")
    os.remove(f"{cwd}/{userId}a.png")
    os.remove(f"{cwd}/{userId}c.png")
    os.remove(f"{cwd}/{userId}f.png")
    os.remove(f"{cwd}/{userId}n.png")

    print("files deleted")


def uploadFile(userId):
    user = userId
    filename = os.path.join("finantialPlanning",f"{user}.pdf")
    uploadFileName = f"financialpdf/{userId}.pdf"
    fullFilePath = "https://stocktick1.s3.ap-south-1.amazonaws.com/" + uploadFileName
    try:
        s3.upload_file(filename, 'stocktick1', uploadFileName)
        try:
            delete_files(userId)
            # pass
        except Exception as e:
            print(e)
        return fullFilePath
    except Exception as e:  # getting permission denied error
        print(e)
        return False
