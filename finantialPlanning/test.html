
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css">
        <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.slim.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"></script>
        
    </head>
    
    <style>
       * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            width: 100%;
        }
        
        .container {
            width: 100%;
            margin-top: 20px;
        }
        
        .mm {
            width: 100%;
            margin-bottom: 5px;
            height: 370px;
            position: relative;
        }
        
        .m1 {
            width: 45%;
            margin-right: 5%;
            display: inline-block;
            height: 300px;
            margin-top: 50px;
        
        }
        
        .m2 {
            width: 45%;
            margin-left: 3%;
            border-top: 2px solid #000;
            display: inline-block;
            height: 300px;
            position: absolute;
            right: 2%;
            margin-top: 50px;
        }
        
        .m2 img {
            position: relative;
            object-fit: contain;
            width: 100%;
            margin-top: 0px;
        }
        
        .m1 h1 {
            font-size: 19px;
            color: #db8f5a;
            margin-top: 10px;
        }
    
        .m2 h1 {
            font-size: 19px;
            color: #db8f5a;
            margin-top: 10px;
        }
        
        .mmm {
            background-color: #f2f2f2;
            margin-top: 0;
            width: 100%;
            padding-top: 6px;
            padding-bottom: 6px;
            padding-left: 10px;
            border-top: 2px solid #000;
        }
        
        .date {
            font-size: 15px;
            font-weight: 500;
            padding-left: 10px;
            padding-top: 5px;
            position: absolute;
            right: 3%;
        }
        
        .lekha {
            padding-left: 10px;
            padding-top: 10px;
        }
        
        .lekha h2 {
            color: #000;
            font-size: 17px;
        }
        
        .tu {
            display: flex;
        }
        
        .tu1 {
            color: #db8f5a;
            width: 47%;
            margin-right: 2%;
            display: inline-block;
        }
        
        .tu1 h1 {
            padding-left: 13px;
            font-size: 19px;
            padding-top: 3px;
        }
        
        .tu1 img,
        .tu2 img {
            object-fit: contain;
            width: 100%;
        }
        
        .tu2 {
            color: #db8f5a;
            width: 47%;
            margin-left: 2%;
            display: inline-block;
        }
        
        .tu2 h1 {
            font-size: 19px;
            padding-left: 10px;
            padding-top: 6px;
            padding-bottom: 10px;
            padding-left: 10px;
            border-top: 2px solid #000;
        }
        
        .tt {
            width: 100%;
        }
        
        .tt h1 {
            background-color: #f2f2f2;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 10px;
            border-top: 2px solid #000;
        }
        
        .same {
            display: flex;
            margin-top: 5px;
        }
        
        .sm {
            width: 45%;
            margin-right: 5%;
            display: inline-block;
        }
        
        .sma{
            width: 45%;
            margin-left: 3%;
            display: inline-block;
        }
        
        .sm1 {
            width: 100%;
            background-color: #f2f2f2;
            position: relative;
            border-top: 2px solid #000;
        
        }
        
        
        
        .sm1 h1 {
            color: #db8f5a;
            font-size: 19px;
            padding-top: 10px;
            padding-left: 15px;
            padding-bottom: 10px;
        }
        
        .sm2 {
            color: #db8f5a;
            background-color: #f2f2f2;
            width: 100%;
            border-top: 2px solid #000;
         
        
        }
        
        .sm2 h1 {
            font-size: 19px;
            padding-top: 6px;
            padding-left: 10px;
            padding-bottom: 10px;
        }
    
        .logo {
            width: 50px;
            height: 60px;
            float: left;
            margin: 3px;
            position: absolute;
            left: 0%;
        }
        
        .logo img{
            width: 100px;
            height: 80px;
            object-fit: contain;
    
        }
        .title {
                font-size: 22px;
                font-weight: bold;
                color: #db8f5a;
                background-color: #f2f2f2;
                padding: 2px;
                width: 300px;
                height: 40px;
                text-align: center;
                border-radius: 3px;
                font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
                transform: translateX(50px);
                margin-left: auto;
                margin-right: auto;
            }
        
        
        .same2 {
        
        }
        
        .part {
            display: flex;
            position: relative;
        }
        
        .pp1 {
            width: 100%;
            padding-bottom: 20px;
            padding-left: 10px;
        }
        
        .pp2 {
            position: absolute;
            margin-top: 5px;
            right: 0;
            top: 0;
        }
        
        .pp2 h1 {
            font-size: 17px;
            padding-left: 21px;
            padding-top: 12px;
        }
        
        .pp1 h1 {
            line-height: 1.5;
        
            font-size: 17px;
            padding-top: 3px;
        }
        
        .imm {
            
            margin-top: 10px;
            margin-right: 0;
            right: -7px;
        }
        
        .mmi {
            width: 100%;
            padding-top: 40px;
            padding-left: 10px;
        }
        
        .mmi h1 {
            font-size: 16px;
            line-height: 1.7;
        }
        
        .end {
            position: relative;
            width: 100%;
            padding-bottom: 20px;
        }
        .em{
            border-top: 2px solid #000;
        }
        
        .em h1 {
            background-color: #f2f2f2;
            font-size: 16px;
            padding-top: 7px;
            color: #db8f5a;
            padding-bottom: 10px;
            padding-left: 10px;
        }
        .aaaab{
            padding-top: 10px;
        }
        .aaaab h2{
            font-size: 15px;
            margin-left: 15px;
            font-weight: 500;
            padding-bottom: 10px;
        }
        .abcd{
            width: 45%;
            margin-right: 5%;
        }
        .high{
            background-color: rgb(107, 215, 107);
            margin-bottom: 5px;
            display: flex;
            justify-content: center;
            font-family: 'Poppins',sans-serif;
            font-weight: 600;
            padding: 3px 0;
        }
        .med{
            background-color: rgb(236, 206, 16);
            margin-bottom: 5px;
            display: flex;
            justify-content: center;
            font-family: 'Poppins',sans-serif;
            font-weight: 600;
            padding: 3px 0;
        }
        .low{
            background-color: rgb(236, 16, 60);
            margin-bottom: 5px;
            display: flex;
            justify-content: center;
            font-family: 'Poppins',sans-serif;
            font-weight: 600;
            padding: 3px 0;
        }
    
    
    </style>
    
    <body>
          <div class="container">
    
    
            <div class="mm">
                <div class="logo">
                    <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/images/Logo+New.png" alt="" srcset="">
                </div>
                <div class="title">
                    Financial Planning Report
                </div>
                <div class="date">
                    2022-08-11 || 16:08:06
                </div>
    
                <div class="m1">
    
                    <div class="mmm">
                        <h1> Family's Vision of Financial Welbeing</h1>
                    </div>
    
    
                    <div class="lekha">
    
                        <h2>1) Be on track to achieve financial goals</h2>
                        <h2>2) Become debt free. pay off home loan</h2>
                        <h2>3) Get adequately insured for all financial risks</h2>
                        <h2>4) Create a comfortable retirement corpus</h2>
                        <h2>5) Have peace of mind around money matters</h2>
                    </div>
    
                </div>
                <div class="m2">
                    <img src="https://thumbs.dreamstime.com/b/environment-earth-day-hands-trees-growing-seedlings-bokeh-green-background-female-hand-holding-tree-nature-field-gra-130247647.jpg" width="100%"
                        height="282" alt="">
                </div>
    
            </div>
    
    
    
            <div class="tu">
    
    
                <div class="tu1">
                    <div class="tt">
                        <h1>Annual Cashflow Breakup</h1>
                    </div>
                    <img src="./22c.png"
                        alt="" width="700" height="350">
                </div>
    
                <div class="tu2">
                    <div class="tt">
                        <h1>Network History</h1>
                    </div>
                    <img src="./22n.png"
                        alt="" width="700" height="300">
                </div>
    
    
            </div>
    
    
    
            <div class="same">
    
    
                <div class="sm">
                    <div class="sm1">
                        <h1>Current Asset Allocation</h1>
                    </div>
                    <img src="./22a.png" alt="" width="400"
                        height="400" style="object-fit: contain;">
                </div>
    
                <div class="sma">
                    <div class="sm2">
                        <h1>Financial Instruments Held</h1>
                    </div>
                    <img src="./22f.png" alt="" width="460"
                        height="300" style="object-fit: contain;">
                </div>
    
            </div>
    
            <div class="same">
    
    
                <div class="sm">
                    <div class="sm1">
                        <h1>Child Marrige Calculator</h1>
                    </div>
                    <div class="aaaab">
                        <h2>Estimated expenditure on child marriage (in Rs) : <b>385180.0</b></h2>
                        <h2>Inflation Rate: <b>7.0</b> </h2>
                        <h2>Age (in years) : <b>16</b></h2>
                        <h2>Expected Age of marriage of your child (in years) : <b>34</b></h2>
                        <h2>Expected Investment Returns (in Rs) : <b>11.0</b></h2>
                        <h2>Full cost of your child marriage (in Rs)  : <b>1301880.0</b> </h2>
                        <h2>Monthly Investments Required (in Rs) : <b>1914.0</b> </h2>
                    </div>
                </div>
    
                <div class="sma">
                    <div class="sm2">
                        <h1>Emergency Fund Calculator</h1>
                    </div>
                    <div class="aaaab">
                        <h2>Living Expenses (p.m.) (in Rs) :  <b>10344.2</b> </h2>
                        <h2>Emergency Rate for (months) : <b>3.0</b>  </h2>
                        <h2>Emergency Fund to be accumulated in (months) (in Rs): <b>22.0</b> </h2>
                        <h2>Investment Return (p.a.) :  <b>25.0</b> </h2>
                        <h2>Emergency Fund you need (in Rs) :  <b>8.2</b> </h2>
                        <h2>Monthly investment Required (in Rs) :  <b>123456.0</b>  </h2>
    
                    </div>
    
                </div>
    
            </div>
    
            <div class="same">
    
    
                <div class="sm">
                    <div class="sm1">
                        <h1>Child Education Calculator</h1>
                    </div>
                    <div class="aaaab">
                        <h2>Current cost of the desired education (in Rs) : <b>10344.2</b> </h2>
                        <h2>Inflation Rate : <b>3.0</b></h2>
                        <h2>Current Age of Child (in years) : <b>22</b></h2>
                        <h2>Age of child when corpus is required (in years) : <b>25</b></h2>
                        <h2>Expected Investment Returns (in Rs) : <b>8.2</b> </h2>
                        <h2>Future cost of your child education (in Rs) : <b>2000000.0</b></h2>
                        <h2>Monthly Investments required (in Rs) :  <b>123456.0</b></h2>
    
                    </div>
    
                </div>
    
                <div class="sma">
                    <div class="sm2">
                        <h1>Retirement Calculator</h1>
                    </div>
                    <div class="aaaab">
                        <h2>How old you are? (in years) : <b>18</b></h2>
                        <h2>How much do you spend in a month? (in Rs): <b>22.4</b> </h2>
                        <h2>Which type of retirement do you want? (in Rs) : <b>Basic</b> </h2>
                        <h2>Time Period (in Rs) : <b>25</b> </h2>
                        <h2>Targeted Goal Amount (in Rs) : <b>8.2</b></h2>
                        <h2>Monthly Investment (in Rs) : <b>2000000.0</b></h2>
    
                    </div>
    
                </div>
    
            </div>
    
            <div class="same">
    
    
                <div class="sm">
                    <div class="sm1">
                        <h1>Financial Goals of Family</h1>
                    </div>
    
                    <div class="part">
                        <div class="pp1">
                            <h1 style="font-size: 17px; padding-top:12px;">Goal</h1>
                            <h1>1)₹40 Lakhs for Velijon's Graduation</h1>
                            <h1>2)₹10 Lakhs for Car Upgrade</h1>
                            <h1>3)₹3 Crores for Retirement Corpus</h1>
                            <h1>4)₹40 Lakhs for Venessa Graduation</h1>
                            <h1 style="font-size: 15px;">5)₹60 Lakhs for 2nd House Downpayment</h1>
    
                        </div>
                        <div class="pp2">
                            <h1>Priority</h1>
                            <div class="imm">
                                <!-- <img src="https://stocktick1.s3.ap-south-1.amazonaws.com/financialpdf/images/level.png"
                                    alt=""> -->
    
                                    <div class="high">High</div>
                                    <div class="high">High</div>
                                    <div class="med">Medium</div>
                                    <div class="med">Medium</div>
                                    <div class="low">Low</div>
                                    
                            </div>
    
    
    
                        </div>
    
    
                    </div>
    
    
                </div>
    
                <div class="sma">
    
    
                    <div class="sm2">
    
    
                        <h1>Actions Recommended(Short-term)</h1>
    
    
                    </div>
                    <div class="mmi">
                        <h1>1.Buy Term Life Insurance Cover of ₹2 Crores for self</h1>
                        <h1>2.Start Sip of ₹30,000 in recommended Shemes</h1>
                        <h1>3.Organize & consolidate all financial documents</h1>
                        <h1>4.Buy Critical Insurance Cover of ₹50 Lakhs for self</h1>
                        <h1>5.Buy Critical Insurance Cover of ₹50 Lakhs for self</h1>
                    </div>
                </div>
            </div>
    
            <div class="end">
    
                <div class="em">
                    <h1>Prepared By Rahul Agarwal,QPEP for Philip Naronha Family on February 16th, 2022</h1>
                </div>
    
                <h1 style="font-size: 15px ; padding-left:10px; padding-top:0px;">&copy; Network FP knowledge Solution
                    Pvt.Ltd,NFP ProMembers are only licensed to use Protool</h1>
    
            </div>
        </div>
        </div>
    
    
    </body>
    
    
    </html>
        