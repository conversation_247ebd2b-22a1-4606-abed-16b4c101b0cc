from operator import mod
from django.db import models
from users.constant import ROW_STATUS, ACTIVE
# Create your models here.


class fpQ1(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    lstatus = models.CharField(
        blank=True, null=True, max_length=10, default="NA")
    children = models.IntegerField(blank=False, null=False)
    parents = models.CharField(max_length=45, blank=True, null=True)

    def __str__(self):
        return str(self.created_by) + "-" + str(self.lstatus)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q1'


class fpQ2(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.Char<PERSON>ield(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    invst_amount = models.IntegerField(blank=False, null=False)
    household_expns = models.IntegerField(blank=False, null=False)
    lifestyle_expns = models.IntegerField(blank=False, null=False)
    surplus = models.IntegerField(blank=False, null=False)
    tax_paid = models.IntegerField(blank=False, null=False)
    emi_paid = models.IntegerField(blank=False, null=False)

    def __str__(self):
        return str(self.created_by) + "-" + str(self.status)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q2'


class fpQ3(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    gold = models.IntegerField(blank=True, null=True)
    equity = models.IntegerField(blank=True, null=True)
    estate = models.IntegerField(blank=True, null=True)
    debt = models.IntegerField(blank=True, null=True)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q3'


class fpQ4(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    md = models.IntegerField(blank=False, null=False)
    mf = models.IntegerField(blank=False, null=False)
    nps = models.IntegerField(blank=False, null=False)
    shares = models.IntegerField(blank=False, null=False)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q4'


class fpQ5(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    goals = models.CharField(blank=True, null=True, max_length=2000)
    priority = models.CharField(blank=True, null=True, max_length=2000)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q5'


class fpQ6(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    goals = models.CharField(max_length=2000)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q6'


class fpQ7(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    img = models.CharField(max_length=400)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q7'


class fpQ8(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    years = models.CharField(max_length=1000, blank=True, null=True)
    assets = models.CharField(max_length=1000, blank=True, null=True)
    liability = models.CharField(max_length=1000, blank=True, null=True)
    net_worth = models.CharField(max_length=1000, blank=True, null=True)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_Q8'


class fpChildMarriageCalc(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    spend = models.FloatField(default=0.0)
    inflation_rate = models.FloatField(default=0.0)
    age = models.IntegerField(default=21)
    marriage_age = models.IntegerField(default=21)
    investment_return = models.FloatField(default=0.0)
    child_marriage_cost = models.FloatField(default=0.0)
    monthly_investment = models.FloatField(default=0.0)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_child_mariage_calc'


class fpEmergencyFundCalc(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    living_expense = models.FloatField(default=0.0)
    emergency_rate = models.FloatField(default=0.0)
    emergency_fund = models.FloatField(default=0.0)
    investment_return = models.FloatField(default=0.0)
    emergency_fund_you_need = models.FloatField(default=0.0)
    monthly_investment_reqd = models.FloatField(default=0.0)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_emergency_fund_calc'


class fpChildEduCalc(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    current_edu_cost = models.FloatField(default=0.0)
    inflation_rate = models.FloatField(default=0.0)
    current_age = models.IntegerField(default=21)
    corpus_age = models.IntegerField(default=28)
    expected_investment_return = models.FloatField(default=0.0)
    future_edu_cost = models.FloatField(default=0.0)
    monthly_investment_reqd = models.FloatField(default=0.0)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_child_edu_calc'


class fpRetirementCalc(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    monthly_income_reqd = models.FloatField(default=0.0)
    inflation_rate = models.FloatField(default=0.0)
    age = models.IntegerField(default=18)
    retirement_age = models.IntegerField(default=68)
    life_expectancy = models.IntegerField(default=98)
    current_investment = models.FloatField(default=0.0)
    return_rate = models.FloatField(default=5.0)
    annual_income_retire = models.FloatField(default=0.0)
    investment_appreciation = models.FloatField(default=0.0)
    corpus_deficit = models.FloatField(default=0.0)
    lumpsum_fund_reqd = models.FloatField(default=0.0)
    monthly_investment_reqd = models.FloatField(default=0.0)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_retirement_calc'


class fpWealthCalc(models.Model):
    id = models.AutoField(primary_key=True)
    created_by = models.CharField(max_length=45, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    modified_by = models.CharField(max_length=45, blank=True, null=True)
    modified_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    status = models.CharField(blank=False, null=False,
                              choices=ROW_STATUS, default=ACTIVE, max_length=10)
    age = models.FloatField(default=0.0)
    pretax = models.FloatField(default=0.0)
    net_wealth = models.FloatField(default=0.0)

    def __str__(self):
        return str(self.created_by)

    class Meta:
        managed = False
        db_table = 'finantialPlanning_wealth_calc'
