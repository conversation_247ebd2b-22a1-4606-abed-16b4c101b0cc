CSS="""
<style>
    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }
    
    body {
        width: 100%;
    }
    
    .container {
        width: 100%;
        margin-top: 30px;

    }
    
    .mm {
        width: 100%;
        margin-bottom: 5px;
        height: 300px;
        position: relative;
    }
    
    .m1 {
        width: 45%;
        margin-right: 5%;
        display: inline-block;
        height: 300px;
        margin-top:50px;
    
    }
    
    .m2 {
        width: 45%;
        margin-left: 3%;
        border-top: 2px solid #000;
        display: inline-block;
        height: 300px;
        position: absolute;
        right: 2%;
        margin-top: 50px;
    }
    
    .m2 img {
        position: relative;
        object-fit: contain;
        width: 100%;
        height: 65%;
        margin-top: 3px;
    }
    
    .m1 h1 {
        font-size: 19px;
        color: #db8f5a;
        margin-top: 10px;
    }
    
    .mmm {
        background-color: #f2f2f2;
        margin-top: 0;
        width: 100%;
        padding-top: 6px;
        padding-bottom: 6px;
        padding-left: 10px;
        border-top: 2px solid #000;
    }
    
    .date {
        font-size: 15px;
        font-weight: 500;
        position: absolute; 
        right: 3%;
        top: 0;
    }
    .column1 {
            
            padding: 10px;
            height: 50px;
            position: relative;
            width: 100%
        }
    
    .lekha {
        padding-left: 10px;
        padding-top: 10px;
    }
    
    .lekha h2 {
        color: #000;
        font-size: 17px;
    }
    
    .tu {
        display: flex;
    }
    
    .tu1 {
        color: #db8f5a;
        width: 45%;
        margin-right: 5%;
        display: inline-block;
    }
    
    .tu1 h1 {
        padding-left: 13px;
        font-size: 19px;
        padding-top: 3px;
    }
    
    .tu1 img,
    .tu2 img {
        object-fit: contain;
    }
    
    .tu2 {
        color: #db8f5a;
        width: 45%;
        margin-left: 3%;
        display: inline-block;
    }
    
    .tu2 h1 {
        font-size: 19px;
        padding-left: 10px;
        padding-top: 6px;
        padding-bottom: 10px;
        padding-left: 10px;
        border-top: 2px solid #000;
    }
    
    .tt {
        width: 100%;
    }
    
    .tt h1 {
        background-color: #f2f2f2;
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 10px;
        border-top: 2px solid #000;
    }
    
    .same {
        display: flex;
        margin-top: 5px;
    }
    
    .sm {
        width: 45%;
        margin-right: 5%;
        display: inline-block;
    }
    
    .sma{
        width: 45%;
        margin-left: 3%;
        display: inline-block;
    }
    
    .sm1 {
        width: 100%;
        background-color: #f2f2f2;
        position: relative;
        border-top: 2px solid #000;
    
    }
    
    
    
    .sm1 h1 {
        color: #db8f5a;
        font-size: 19px;
        padding-top: 10px;
        padding-left: 15px;
        padding-bottom: 10px;
    }
    
    .sm2 {
        color: #db8f5a;
        background-color: #f2f2f2;
        width: 100%;
        border-top: 2px solid #000;
     
    
    }
    .logo {
            width: 50px;
            height: 50px;
            float: left;
            margin: 3px;
            position: absolute;
            left: 0%;
        }
    
    .logo img{
        width: 100px;
        height: 50px;

    }
    .title {
            font-size: 22px;
            font-weight: bold;
            color: #db8f5a;
            background-color: #f2f2f2;
            padding: 2px;
            width: 300px;
            height: 40px;
            text-align: center;
            border-radius: 3px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            transform: translateX(50px);
            margin-left: auto;
            margin-right: auto;
        }

    .sm2 h1 {
        font-size: 19px;
        padding-top: 6px;
        padding-left: 10px;
        padding-bottom: 10px;
    }
    
    
    
    .same2 {
    
    }
    
    .part {
        display: flex;
        position: relative;
    }
    
    .pp1 {
        width: 100%;
        padding-bottom: 20px;
        padding-left: 10px;
    }
    
    .pp2 {
        position: absolute;
        margin-top: 5px;
        right: 0;
        top: 0;
    }
    
    .pp2 h1 {
        font-size: 17px;
        padding-left: 21px;
        padding-top: 12px;
    }
    
    .pp1 h1 {
        line-height: 1.5;
    
        font-size: 17px;
    }
    
    .imm {
        
        margin-top: 10px;
        margin-right: 0;
        right: -7px;
    }
    
    .mmi {
        width: 100%;
        padding-top: 40px;
        padding-left: 10px;
    }
    
    .mmi h1 {
        font-size: 16px;
        line-height: 1.7;
    }
    
    .end {
        position: relative;
        width: 100%;
        padding-bottom: 20px;
    }
    .em{
        border-top: 2px solid #000;
    }
    
    .em h1 {
        background-color: #f2f2f2;
        font-size: 16px;
        padding-top: 7px;
        color: #db8f5a;
        padding-bottom: 10px;
        padding-left: 10px;
    }
    
    
</style>
"""