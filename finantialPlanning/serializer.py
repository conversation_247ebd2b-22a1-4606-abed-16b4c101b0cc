from .models import *
from rest_framework import serializers


class fpQ1Serializer(serializers.ModelSerializer):
    class Meta:
        model = fpQ1
        fields = '__all__'

class fpQ2Serializer(serializers.ModelSerializer):
    class Meta:
        model = fpQ2
        fields = '__all__'

class fpQ3Serializer(serializers.ModelSerializer):
    class Meta:
        model = fpQ3
        fields = '__all__'

class fpQ4Serializer(serializers.ModelSerializer):
    class Meta:
        model = fpQ4
        fields = '__all__'

class fpQ5Serializer(serializers.ModelSerializer):
    class Meta:
        model = fpQ5
        fields = '__all__'

class fpQ8Serializer(serializers.ModelSerializer):
    class Meta:
        model = fpQ8
        fields = '__all__'

class fpChildMarriageCalcSerializer(serializers.ModelSerializer):
    class Meta:
        model = fpChildMarriageCalc
        fields = '__all__'

class fpEmergencyFundCalcSerializer(serializers.ModelSerializer):
    class Meta:
        model = fpEmergencyFundCalc
        fields = '__all__'

class fpChildEduCalcSerializer(serializers.ModelSerializer):
    class Meta:
        model = fpChildEduCalc
        fields = '__all__'

class fpRetirementCalcSerializer(serializers.ModelSerializer):
    class Meta:
        model = fpRetirementCalc
        fields = '__all__'

class fpWealthCalcSerializer(serializers.ModelSerializer):
    class Meta:
        model = fpWealthCalc
        fields = '__all__'