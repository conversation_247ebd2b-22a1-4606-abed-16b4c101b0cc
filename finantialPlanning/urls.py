from django.urls import path, include
from .views import *

urlpatterns = [
    path('finnantial-Q1', FinnantialQuestion1.as_view()),
    path('finnantial-Q2', FinnantialQuestion2.as_view()),
    path('finnantial-Q3', finantialPlanning_Q3.as_view()),
    path('finnantial-Q4', finantialPlanning_Q4.as_view()),
    path('finnantial-Q5', finantialPlanning_Q5.as_view()),
    path('finnantial-Q6', finantialPlanning_Q6.as_view()),
    path('finnantial-Q7', finantialPlanning_Q7.as_view()),
    path('finnantial-Q8', finantialPlanning_Q8.as_view()),
    path('finnantial-ChildMarriage', finantialPlanning_ChildMarriage.as_view()),
    path('finnantial-EmergencyFund', finantialPlanning_EmergencyFundCalc.as_view()),
    path('finnantial-ChildEduCalc', finantialPlanning_ChildEduCalc.as_view()),
    path('finnantial-RetirementCalc', finantialPlanning_RetirementCalc.as_view()),
    path('pdf', ReportPdf.as_view()),
]
